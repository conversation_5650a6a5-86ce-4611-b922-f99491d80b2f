# BuyStep UI/UX Design Guide

**Version:** 1.0 (Tailwind CSS v4)
**Last Updated:** 2025-10-09
**Purpose:** Comprehensive design system documentation to replicate buystep_ui design EXACTLY in Rails application

---

## Table of Contents

1. [Design Philosophy](#design-philosophy)
2. [Color System](#color-system)
3. [Typography](#typography)
4. [Spacing & Layout](#spacing--layout)
5. [Component Library](#component-library)
6. [Layout Patterns](#layout-patterns)
7. [Interactive Behaviors](#interactive-behaviors)
8. [Responsive Design](#responsive-design)
9. [Icons & Assets](#icons--assets)
10. [Rails Integration](#rails-integration)

---

## Design Philosophy

### African-First Design Principles

BuyStep's design system is built with African users at the center:

- **Mobile-First**: Optimized for feature phones and slow connections
- **Cultural Colors**: Green (growth, prosperity), Orange (warmth, energy), Purple (innovation)
- **Minimal Data Usage**: Efficient CSS, optimized images, progressive enhancement
- **Offline-Ready**: Graceful degradation when connectivity is poor
- **USSD Integration**: Alternative access methods for users without smartphones

### Core Design Values

1. **Clarity**: Every element serves a clear purpose
2. **Accessibility**: WCAG 2.1 AA compliance minimum
3. **Performance**: Sub-3-second load times on 3G networks
4. **Trust**: Security indicators, testimonials, professional polish
5. **Delight**: Subtle animations, smooth transitions, thoughtful micro-interactions

---

## Color System

### Primary Colors (Green - African Growth)

Represents prosperity, growth, and financial success in African markets.

```css
/* Tailwind v4 @theme Configuration */
--color-primary-50: #f0fdf4;   /* Lightest tint - backgrounds, hover states */
--color-primary-100: #dcfce7;  /* Light tint - subtle backgrounds */
--color-primary-200: #bbf7d0;  /* Soft green - disabled states */
--color-primary-300: #86efac;  /* Medium light - borders, decorative */
--color-primary-400: #4ade80;  /* Medium - secondary actions */
--color-primary-500: #22c55e;  /* Base primary - buttons, links, brand */
--color-primary-600: #16a34a;  /* Dark primary - button hover, emphasis */
--color-primary-700: #15803d;  /* Darker - text on light backgrounds */
--color-primary-800: #166534;  /* Deep green - headings */
--color-primary-900: #14532d;  /* Deepest - high contrast text */
--color-primary-950: #052e16;  /* Near black - maximum contrast */
```

**Usage Guidelines:**
- **500**: Primary buttons, active states, brand elements
- **600**: Hover states for primary buttons, emphasis
- **100**: Subtle backgrounds, card highlights
- **700-900**: Text on light backgrounds
- **50**: Very light backgrounds, hover states on white

### Secondary Colors (Orange - Energy & Warmth)

Represents warmth, community, and approachable energy.

```css
--color-secondary-50: #fff7ed;   /* Lightest - backgrounds */
--color-secondary-100: #ffedd5;  /* Light - subtle highlights */
--color-secondary-200: #fed7aa;  /* Soft orange - borders */
--color-secondary-300: #fdba74;  /* Medium light - decorative */
--color-secondary-400: #fb923c;  /* Medium - secondary emphasis */
--color-secondary-500: #f97316;  /* Base secondary - CTAs, highlights */
--color-secondary-600: #ea580c;  /* Dark - hover states */
--color-secondary-700: #c2410c;  /* Darker - text emphasis */
--color-secondary-800: #9a3412;  /* Deep orange - headings */
--color-secondary-900: #7c2d12;  /* Deepest - high contrast */
--color-secondary-950: #431407;  /* Near black */
```

**Usage Guidelines:**
- **500**: Secondary buttons, call-to-action elements
- **600**: Hover states, urgent actions
- **100-200**: Warning backgrounds, notification highlights
- **700-800**: Text on light backgrounds

### Accent Colors (Purple - Modern Innovation)

Represents technology, innovation, and premium features.

```css
--color-accent-50: #faf5ff;   /* Lightest - backgrounds */
--color-accent-100: #f3e8ff;  /* Light - subtle highlights */
--color-accent-200: #e9d5ff;  /* Soft purple - borders */
--color-accent-300: #d8b4fe;  /* Medium light - decorative */
--color-accent-400: #c084fc;  /* Medium - badges, tags */
--color-accent-500: #8b5cf6;  /* Base accent - premium features */
--color-accent-600: #7c3aed;  /* Dark - hover states */
--color-accent-700: #6d28d9;  /* Darker - emphasis */
--color-accent-800: #5b21b6;  /* Deep purple - headings */
--color-accent-900: #4c1d95;  /* Deepest - high contrast */
--color-accent-950: #2e1065;  /* Near black */
```

**Usage Guidelines:**
- **500**: Premium features, accent elements, badges
- **600**: Hover states for accent buttons
- **100-200**: Info backgrounds, subtle highlights
- **700-800**: Text emphasis on light backgrounds

### Semantic Colors

```css
/* Success - Uses primary green */
--color-success: #22c55e; /* primary-500 */

/* Warning - Uses secondary orange */
--color-warning: #f97316; /* secondary-500 */

/* Error - Red */
--color-error: #ef4444; /* red-500 */

/* Info - Uses accent purple */
--color-info: #8b5cf6; /* accent-500 */
```

---

## Typography

### Font Families

**Display Font - Montserrat**
```css
--font-display: 'Montserrat', sans-serif;
/* Weights: 600 (SemiBold), 700 (Bold), 800 (ExtraBold) */
```

**Usage:**
- Page headings (H1, H2)
- Hero section text
- Brand name ("BuyStep")
- Section titles
- Call-to-action text

**Body Font - Inter**
```css
--font-sans: 'Inter', sans-serif;
/* Weights: 400 (Regular), 500 (Medium), 600 (SemiBold) */
```

**Usage:**
- Body text
- Paragraphs
- Form labels
- Navigation links
- Buttons (except hero CTAs)
- Cards and descriptions

### Font Scale

```css
/* Responsive Typography */

/* Mobile (< 640px) */
.text-xs { font-size: 0.75rem; line-height: 1rem; }      /* 12px */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }  /* 14px */
.text-base { font-size: 1rem; line-height: 1.5rem; }     /* 16px */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }  /* 18px */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }   /* 20px */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }      /* 24px */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* 30px */
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }   /* 36px */

/* Desktop (>= 640px) - Scale increases */
.sm\:text-5xl { font-size: 3rem; line-height: 1; }       /* 48px */
.sm\:text-6xl { font-size: 3.75rem; line-height: 1; }    /* 60px */
```

### Typography Patterns

**Hero Heading**
```html
<h1 class="text-4xl sm:text-5xl md:text-6xl font-display font-bold text-white">
  Empower Your Financial Future
</h1>
```

**Section Heading**
```html
<h2 class="text-3xl sm:text-4xl font-display font-bold text-gray-800 text-center mb-4">
  How It Works
</h2>
```

**Body Text**
```html
<p class="text-base sm:text-lg text-gray-600 leading-relaxed">
  BuyStep makes it easy to save, invest, and grow your money through community-driven circles.
</p>
```

**Small Print**
```html
<p class="text-xs sm:text-sm text-gray-500">
  Terms and conditions apply. See our Privacy Policy for details.
</p>
```

### Line Height Guidelines

- **Headings**: `leading-tight` (1.25) or `leading-snug` (1.375)
- **Body text**: `leading-relaxed` (1.625) or `leading-loose` (2)
- **UI text**: `leading-normal` (1.5)

---

## Spacing & Layout

### Spacing Scale

BuyStep follows Tailwind's default spacing scale (0.25rem = 4px increments):

```css
/* Common spacing values */
.p-2  { padding: 0.5rem; }   /* 8px */
.p-4  { padding: 1rem; }     /* 16px */
.p-6  { padding: 1.5rem; }   /* 24px */
.p-8  { padding: 2rem; }     /* 32px */
.p-12 { padding: 3rem; }     /* 48px */
.p-16 { padding: 4rem; }     /* 64px */
.p-20 { padding: 5rem; }     /* 80px */
```

### Layout Containers

**Max Width Container**
```html
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <!-- Content constrained to 1280px -->
</div>
```

**Section Padding**
```html
<section class="py-12 sm:py-16 lg:py-20">
  <!-- Responsive vertical padding -->
</section>
```

### Grid Patterns

**Responsive Grid (1/2/3/4 columns)**
```html
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
  <!-- Cards or items -->
</div>
```

**Common Grid Patterns:**
- **Features**: `grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8`
- **Stats**: `grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4`
- **Testimonials**: `grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6`
- **Form**: `grid-cols-1 md:grid-cols-2 gap-4`

---

## Component Library

### Buttons

#### Primary Button
```html
<button class="btn-primary">
  Sign Up Now
</button>
```

```css
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800
         text-white font-medium
         px-6 py-3 rounded-lg
         transition-all duration-200
         shadow-md hover:shadow-lg
         focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}
```

#### Secondary Button
```html
<button class="btn-secondary">
  Learn More
</button>
```

```css
.btn-secondary {
  @apply border-2 border-primary-600 text-primary-600
         hover:bg-primary-600 hover:text-white
         font-medium px-6 py-3 rounded-lg
         transition-all duration-200
         focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}
```

#### Accent Button
```html
<button class="btn-accent">
  Premium Feature
</button>
```

```css
.btn-accent {
  @apply bg-accent-600 hover:bg-accent-700 active:bg-accent-800
         text-white font-medium
         px-6 py-3 rounded-lg
         transition-all duration-200
         shadow-md hover:shadow-lg
         focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2;
}
```

#### Button Sizes
```css
/* Small button */
.btn-sm { @apply px-4 py-2 text-sm; }

/* Default button */
.btn-md { @apply px-6 py-3 text-base; }

/* Large button */
.btn-lg { @apply px-8 py-4 text-lg; }
```

### Form Components

#### Form Input
```html
<div class="mb-4">
  <label class="form-label" for="email">
    Email Address
  </label>
  <input
    type="email"
    id="email"
    class="form-input"
    placeholder="Enter your email"
  >
  <p class="form-error hidden">Please enter a valid email address</p>
</div>
```

```css
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg
         focus:ring-2 focus:ring-primary-500 focus:border-primary-500
         transition-all duration-200
         placeholder:text-gray-400;
}

.form-error {
  @apply text-red-500 text-sm mt-1;
}

/* Error state */
.form-input.error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}
```

#### Select Dropdown
```html
<select class="form-select">
  <option>Choose an option</option>
  <option value="1">Option 1</option>
</select>
```

```css
.form-select {
  @apply form-input appearance-none
         bg-white bg-no-repeat bg-right
         pr-10;
  background-image: url("data:image/svg+xml,..."); /* Down arrow icon */
}
```

#### Checkbox
```html
<label class="form-checkbox-label">
  <input type="checkbox" class="form-checkbox">
  <span>Remember me</span>
</label>
```

```css
.form-checkbox {
  @apply h-4 w-4 text-primary-600 border-gray-300 rounded
         focus:ring-2 focus:ring-primary-500;
}

.form-checkbox-label {
  @apply flex items-center text-sm text-gray-700 cursor-pointer;
}
```

### Cards

#### Basic Card
```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Card Title</h3>
  </div>
  <div class="card-body">
    <p class="card-text">Card content goes here.</p>
  </div>
  <div class="card-footer">
    <button class="btn-primary">Action</button>
  </div>
</div>
```

```css
.card {
  @apply bg-white rounded-xl shadow-md overflow-hidden
         transition-all duration-200
         hover:shadow-xl hover:-translate-y-1;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-title {
  @apply text-lg font-semibold text-gray-800;
}

.card-body {
  @apply px-6 py-4;
}

.card-text {
  @apply text-gray-600;
}

.card-footer {
  @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
}
```

#### Feature Card (with Icon)
```html
<div class="card hover-lift">
  <div class="card-body text-center">
    <div class="icon-circle mx-auto mb-4">
      <i class="fas fa-users text-2xl"></i>
    </div>
    <h3 class="text-xl font-semibold mb-2">Join Circles</h3>
    <p class="text-gray-600">Connect with others to save and invest together.</p>
  </div>
</div>
```

```css
.icon-circle {
  @apply bg-primary-100 text-primary-600
         w-16 h-16 rounded-full
         flex items-center justify-center;
}

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
}
```

#### Stats Card
```html
<div class="card">
  <div class="card-body">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm text-gray-600 mb-1">Total Savings</p>
        <p class="text-2xl font-bold text-gray-800">KES 45,000</p>
        <p class="text-xs text-green-600 mt-1">
          <i class="fas fa-arrow-up"></i> 12% increase
        </p>
      </div>
      <div class="icon-circle">
        <i class="fas fa-piggy-bank text-2xl"></i>
      </div>
    </div>
  </div>
</div>
```

### Alerts

#### Success Alert
```html
<div class="alert alert-success">
  <i class="fas fa-check-circle"></i>
  <span>Your changes have been saved successfully!</span>
</div>
```

```css
.alert {
  @apply px-4 py-3 rounded-lg flex items-center gap-3 mb-4;
}

.alert-success {
  @apply bg-green-50 text-green-800 border border-green-200;
}

.alert-warning {
  @apply bg-orange-50 text-orange-800 border border-orange-200;
}

.alert-error {
  @apply bg-red-50 text-red-800 border border-red-200;
}

.alert-info {
  @apply bg-blue-50 text-blue-800 border border-blue-200;
}
```

### Badges

```html
<span class="badge badge-primary">Active</span>
<span class="badge badge-secondary">Pending</span>
<span class="badge badge-accent">Premium</span>
```

```css
.badge {
  @apply inline-flex items-center px-3 py-1 rounded-full
         text-xs font-medium;
}

.badge-primary {
  @apply bg-primary-100 text-primary-800;
}

.badge-secondary {
  @apply bg-secondary-100 text-secondary-800;
}

.badge-accent {
  @apply bg-accent-100 text-accent-800;
}
```

### Tables

#### Desktop Table
```html
<div class="overflow-x-auto">
  <table class="table">
    <thead class="table-header">
      <tr>
        <th class="table-cell">Name</th>
        <th class="table-cell">Amount</th>
        <th class="table-cell">Status</th>
      </tr>
    </thead>
    <tbody>
      <tr class="table-row">
        <td class="table-cell">John Doe</td>
        <td class="table-cell">KES 5,000</td>
        <td class="table-cell">
          <span class="badge badge-primary">Active</span>
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

```css
.table {
  @apply w-full border-collapse;
}

.table-header {
  @apply bg-gray-50 border-b-2 border-gray-200;
}

.table-cell {
  @apply px-6 py-3 text-left text-sm;
}

.table-header .table-cell {
  @apply font-semibold text-gray-700;
}

.table-row {
  @apply border-b border-gray-200 hover:bg-gray-50
         transition-colors duration-150;
}
```

#### Mobile Card View (< 768px)
```html
<div class="mobile-card-list md:hidden">
  <div class="mobile-card">
    <div class="flex justify-between items-center mb-2">
      <span class="font-semibold">John Doe</span>
      <span class="badge badge-primary">Active</span>
    </div>
    <div class="text-sm text-gray-600">
      Amount: <span class="font-medium">KES 5,000</span>
    </div>
  </div>
</div>
```

```css
@media (max-width: 768px) {
  .mobile-card {
    @apply bg-white rounded-lg shadow-md p-4 mb-3;
  }
}
```

---

## Layout Patterns

### Landing Page Layout

#### Navigation Bar
```html
<nav class="fixed top-0 w-full bg-white shadow-sm z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <span class="text-2xl font-display font-bold text-primary-600">
          BuyStep
        </span>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center gap-8">
        <a href="#" class="nav-link">Features</a>
        <a href="#" class="nav-link">How It Works</a>
        <a href="#" class="nav-link">About</a>
        <button class="btn-primary">Get Started</button>
      </div>

      <!-- Mobile Menu Button -->
      <button class="md:hidden" id="mobile-menu-btn">
        <i class="fas fa-bars text-2xl"></i>
      </button>
    </div>
  </div>
</nav>
```

```css
.nav-link {
  @apply text-gray-700 hover:text-primary-600
         font-medium transition-colors duration-200;
}
```

#### Hero Section
```html
<section class="pt-24 pb-12 sm:pt-32 sm:pb-16 bg-gradient-to-r from-primary-600 to-accent-700">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Hero Content -->
      <div class="text-white">
        <h1 class="text-4xl sm:text-5xl lg:text-6xl font-display font-bold mb-6">
          Empower Your Financial Future
        </h1>
        <p class="text-lg sm:text-xl mb-8 opacity-90">
          Join thousands of Africans building wealth through community savings and investments.
        </p>
        <div class="flex flex-wrap gap-4">
          <button class="btn-primary bg-white text-primary-600 hover:bg-gray-100">
            Get Started
          </button>
          <button class="btn-secondary border-white text-white hover:bg-white hover:text-primary-600">
            Learn More
          </button>
        </div>
      </div>

      <!-- Hero Image -->
      <div class="hidden lg:block">
        <img src="/images/hero-illustration.svg" alt="Hero" class="w-full">
      </div>
    </div>
  </div>
</section>
```

#### Features Section
```html
<section class="py-16 sm:py-20 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-12">
      <h2 class="text-3xl sm:text-4xl font-display font-bold text-gray-800 mb-4">
        Why Choose BuyStep?
      </h2>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        Discover the features that make saving and investing easier for African communities.
      </p>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Feature Card -->
      <div class="card hover-lift text-center">
        <div class="card-body">
          <div class="icon-circle mx-auto mb-4">
            <i class="fas fa-users text-2xl"></i>
          </div>
          <h3 class="text-xl font-semibold mb-2">Community Circles</h3>
          <p class="text-gray-600">
            Join or create savings circles with friends, family, or like-minded individuals.
          </p>
        </div>
      </div>
      <!-- More feature cards... -->
    </div>
  </div>
</section>
```

#### Footer
```html
<footer class="bg-gray-900 text-gray-300 py-12">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Brand Column -->
      <div>
        <span class="text-2xl font-display font-bold text-white">BuyStep</span>
        <p class="mt-4 text-sm">
          Empowering financial futures across Africa through community-driven savings and investments.
        </p>
      </div>

      <!-- Links Columns -->
      <div>
        <h4 class="font-semibold text-white mb-4">Product</h4>
        <ul class="space-y-2 text-sm">
          <li><a href="#" class="hover:text-white transition-colors">Features</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Pricing</a></li>
        </ul>
      </div>

      <!-- More columns... -->
    </div>

    <!-- Bottom Bar -->
    <div class="mt-8 pt-8 border-t border-gray-800 text-sm text-center">
      <p>&copy; 2025 BuyStep. All rights reserved.</p>
    </div>
  </div>
</footer>
```

### Authentication Layout

#### Centered Auth Card
```html
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full">
    <!-- Logo -->
    <div class="text-center mb-8">
      <span class="text-3xl font-display font-bold text-primary-600">BuyStep</span>
      <p class="mt-2 text-gray-600">Sign in to your account</p>
    </div>

    <!-- Auth Card -->
    <div class="card">
      <div class="card-body p-8">
        <!-- Alert (hidden by default) -->
        <div class="alert alert-error hidden" id="auth-alert">
          <i class="fas fa-exclamation-circle"></i>
          <span>Invalid credentials</span>
        </div>

        <!-- Form -->
        <form>
          <!-- Email/Phone Input -->
          <div class="mb-4">
            <label class="form-label" for="email-phone">
              Email or Phone Number
            </label>
            <input
              type="text"
              id="email-phone"
              class="form-input"
              placeholder="Enter email or phone"
            >
          </div>

          <!-- Password Input with Toggle -->
          <div class="mb-4">
            <label class="form-label" for="password">Password</label>
            <div class="relative">
              <input
                type="password"
                id="password"
                class="form-input pr-10"
                placeholder="Enter password"
              >
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                onclick="togglePasswordVisibility('password')"
              >
                <i class="fas fa-eye text-gray-400"></i>
              </button>
            </div>
          </div>

          <!-- Remember Me & Forgot Password -->
          <div class="flex items-center justify-between mb-6">
            <label class="form-checkbox-label">
              <input type="checkbox" class="form-checkbox">
              <span>Remember me</span>
            </label>
            <a href="#" class="text-sm text-primary-600 hover:text-primary-700">
              Forgot password?
            </a>
          </div>

          <!-- Submit Button -->
          <button type="submit" class="btn-primary w-full mb-4">
            Sign In
          </button>

          <!-- Alternative Login Options -->
          <div class="space-y-2">
            <button type="button" class="btn-secondary w-full">
              <i class="fab fa-whatsapp mr-2"></i>
              Sign in with WhatsApp
            </button>
            <button type="button" class="btn-secondary w-full">
              <i class="fas fa-phone mr-2"></i>
              Sign in with USSD
            </button>
          </div>
        </form>

        <!-- Sign Up Link -->
        <div class="mt-6 text-center text-sm">
          <span class="text-gray-600">Don't have an account?</span>
          <a href="#" class="text-primary-600 hover:text-primary-700 font-medium ml-1">
            Sign up
          </a>
        </div>
      </div>
    </div>

    <!-- Footer Links -->
    <div class="mt-6 text-center text-xs text-gray-500">
      <a href="#" class="hover:text-gray-700">Terms</a>
      <span class="mx-2">•</span>
      <a href="#" class="hover:text-gray-700">Privacy</a>
    </div>
  </div>
</div>
```

### Dashboard Layout

#### Sidebar + Top Nav Pattern
```html
<div class="flex h-screen bg-gray-100">
  <!-- Sidebar (Desktop) -->
  <aside class="hidden md:flex md:flex-col w-64 bg-white shadow-lg">
    <!-- Logo -->
    <div class="p-4 border-b">
      <span class="text-2xl font-display font-bold text-primary-600">BuyStep</span>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 p-4 space-y-1 overflow-y-auto">
      <!-- Active Item -->
      <a href="#" class="nav-item active">
        <i class="fas fa-home w-5"></i>
        <span>Dashboard</span>
      </a>

      <!-- Regular Items -->
      <a href="#" class="nav-item">
        <i class="fas fa-users w-5"></i>
        <span>My Circles</span>
      </a>

      <a href="#" class="nav-item">
        <i class="fas fa-exchange-alt w-5"></i>
        <span>Transactions</span>
      </a>

      <!-- More items... -->
    </nav>

    <!-- User Profile -->
    <div class="p-4 border-t">
      <div class="flex items-center gap-3">
        <img src="/images/avatar.jpg" alt="User" class="w-10 h-10 rounded-full">
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-800 truncate">John Doe</p>
          <p class="text-xs text-gray-500 truncate"><EMAIL></p>
        </div>
      </div>
    </div>
  </aside>

  <!-- Main Content Area -->
  <div class="flex-1 flex flex-col overflow-hidden">
    <!-- Top Navigation Bar -->
    <header class="bg-white shadow-sm">
      <div class="flex items-center justify-between px-6 py-4">
        <!-- Mobile Menu Button -->
        <button class="md:hidden" id="mobile-menu-btn">
          <i class="fas fa-bars text-2xl"></i>
        </button>

        <!-- Search Bar -->
        <div class="flex-1 max-w-md mx-4">
          <div class="relative">
            <input
              type="text"
              class="form-input pl-10"
              placeholder="Search..."
            >
            <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        <!-- Right Actions -->
        <div class="flex items-center gap-4">
          <!-- Notifications -->
          <button class="relative">
            <i class="fas fa-bell text-xl text-gray-600"></i>
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              3
            </span>
          </button>

          <!-- User Dropdown -->
          <div class="relative">
            <button class="flex items-center gap-2" id="user-menu-btn">
              <img src="/images/avatar.jpg" alt="User" class="w-8 h-8 rounded-full">
              <i class="fas fa-chevron-down text-sm text-gray-600"></i>
            </button>

            <!-- Dropdown Menu (Hidden by default) -->
            <div class="dropdown-menu hidden">
              <a href="#" class="dropdown-item">
                <i class="fas fa-user w-5"></i>
                <span>Profile</span>
              </a>
              <a href="#" class="dropdown-item">
                <i class="fas fa-cog w-5"></i>
                <span>Settings</span>
              </a>
              <hr class="my-2">
              <a href="#" class="dropdown-item text-red-600">
                <i class="fas fa-sign-out-alt w-5"></i>
                <span>Logout</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Page Content -->
    <main class="flex-1 overflow-y-auto p-6">
      <!-- Page Header -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Dashboard</h1>
        <p class="text-gray-600">Welcome back, John!</p>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Stats cards here... -->
      </div>

      <!-- Additional Content -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Charts, tables, etc. -->
      </div>
    </main>
  </div>
</div>

<!-- Mobile Bottom Navigation (<=768px) -->
<nav class="mobile-bottom-nav">
  <a href="#" class="mobile-nav-item active">
    <i class="fas fa-home text-xl"></i>
    <span class="text-xs">Home</span>
  </a>
  <a href="#" class="mobile-nav-item">
    <i class="fas fa-users text-xl"></i>
    <span class="text-xs">Circles</span>
  </a>
  <a href="#" class="mobile-nav-item">
    <i class="fas fa-plus-circle text-2xl"></i>
    <span class="text-xs">Add</span>
  </a>
  <a href="#" class="mobile-nav-item">
    <i class="fas fa-bell text-xl"></i>
    <span class="text-xs">Alerts</span>
  </a>
  <a href="#" class="mobile-nav-item">
    <i class="fas fa-user text-xl"></i>
    <span class="text-xs">Profile</span>
  </a>
</nav>
```

```css
/* Navigation Item Styles */
.nav-item {
  @apply flex items-center gap-3 px-4 py-3 rounded-lg
         text-gray-700 hover:bg-gray-100 hover:text-primary-600
         transition-all duration-200 font-medium;
}

.nav-item.active {
  @apply bg-primary-600 text-white hover:bg-primary-700 hover:text-white;
}

/* Dropdown Menu */
.dropdown-menu {
  @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg
         border border-gray-200 py-2 z-50;
}

.dropdown-item {
  @apply flex items-center gap-3 px-4 py-2
         text-gray-700 hover:bg-gray-100
         transition-colors duration-150;
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
  display: none;
}

@media (max-width: 768px) {
  .mobile-bottom-nav {
    @apply flex fixed bottom-0 left-0 right-0
           bg-white border-t border-gray-200 shadow-lg
           justify-around items-center h-16 z-50;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center gap-1
           text-gray-600 hover:text-primary-600
           transition-colors duration-200 flex-1;
  }

  .mobile-nav-item.active {
    @apply text-primary-600;
  }
}
```

---

## Interactive Behaviors

### JavaScript Utilities

#### Mobile Menu Toggle
```javascript
// Mobile menu creation and toggle
function createMobileBottomNav() {
  const nav = document.createElement('nav');
  nav.className = 'mobile-bottom-nav';
  nav.innerHTML = `
    <a href="/" class="mobile-nav-item active">
      <i class="fas fa-home text-xl"></i>
      <span class="text-xs">Home</span>
    </a>
    <!-- More items... -->
  `;
  document.body.appendChild(nav);
}

// Call on dashboard pages
if (window.innerWidth <= 768) {
  createMobileBottomNav();
}
```

#### Form Validation
```javascript
function validateForm(formId) {
  const form = document.getElementById(formId);
  const inputs = form.querySelectorAll('input[required]');
  let isValid = true;

  inputs.forEach(input => {
    const errorElement = input.parentElement.querySelector('.form-error');

    if (!input.value.trim()) {
      input.classList.add('error');
      if (errorElement) errorElement.classList.remove('hidden');
      isValid = false;
    } else {
      input.classList.remove('error');
      if (errorElement) errorElement.classList.add('hidden');
    }

    // Email validation
    if (input.type === 'email' && input.value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(input.value)) {
        input.classList.add('error');
        if (errorElement) {
          errorElement.textContent = 'Please enter a valid email';
          errorElement.classList.remove('hidden');
        }
        isValid = false;
      }
    }
  });

  return isValid;
}

// Usage
form.addEventListener('submit', (e) => {
  e.preventDefault();
  if (validateForm('login-form')) {
    // Submit form
  }
});
```

#### Toast Notifications
```javascript
function showToast(message, type = 'info', duration = 3000) {
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;

  const icons = {
    success: 'fa-check-circle',
    error: 'fa-exclamation-circle',
    warning: 'fa-exclamation-triangle',
    info: 'fa-info-circle'
  };

  toast.innerHTML = `
    <i class="fas ${icons[type]}"></i>
    <span>${message}</span>
  `;

  document.body.appendChild(toast);

  // Trigger animation
  setTimeout(() => toast.classList.add('show'), 10);

  // Auto dismiss
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => toast.remove(), 300);
  }, duration);
}

// Usage
showToast('Changes saved successfully!', 'success');
```

```css
.toast {
  @apply fixed top-4 right-4 z-50
         bg-white shadow-lg rounded-lg
         px-4 py-3 flex items-center gap-3
         transform translate-x-full
         transition-transform duration-300;
}

.toast.show {
  @apply translate-x-0;
}

.toast-success { @apply border-l-4 border-green-500; }
.toast-error { @apply border-l-4 border-red-500; }
.toast-warning { @apply border-l-4 border-orange-500; }
.toast-info { @apply border-l-4 border-blue-500; }
```

#### Animated Counters
```javascript
function animateCounter(element, target, duration = 2000) {
  const start = 0;
  const increment = target / (duration / 16); // 60fps
  let current = start;

  const timer = setInterval(() => {
    current += increment;
    if (current >= target) {
      element.textContent = target.toLocaleString();
      clearInterval(timer);
    } else {
      element.textContent = Math.floor(current).toLocaleString();
    }
  }, 16);
}

// Trigger on scroll into view
const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const target = parseInt(entry.target.dataset.target);
      animateCounter(entry.target, target);
      observer.unobserve(entry.target);
    }
  });
});

document.querySelectorAll('[data-counter]').forEach(el => {
  observer.observe(el);
});
```

#### Password Visibility Toggle
```javascript
function togglePasswordVisibility(inputId) {
  const input = document.getElementById(inputId);
  const icon = input.nextElementSibling.querySelector('i');

  if (input.type === 'password') {
    input.type = 'text';
    icon.classList.remove('fa-eye');
    icon.classList.add('fa-eye-slash');
  } else {
    input.type = 'password';
    icon.classList.remove('fa-eye-slash');
    icon.classList.add('fa-eye');
  }
}
```

#### Modal Management
```javascript
function openModal(modalId) {
  const modal = document.getElementById(modalId);
  modal.classList.remove('hidden');
  document.body.style.overflow = 'hidden';

  // Animate in
  setTimeout(() => {
    modal.classList.add('modal-open');
  }, 10);
}

function closeModal(modalId) {
  const modal = document.getElementById(modalId);
  modal.classList.remove('modal-open');

  setTimeout(() => {
    modal.classList.add('hidden');
    document.body.style.overflow = 'auto';
  }, 300);
}

// Close on backdrop click
document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
  backdrop.addEventListener('click', (e) => {
    if (e.target === backdrop) {
      closeModal(backdrop.closest('.modal').id);
    }
  });
});
```

```html
<!-- Modal Structure -->
<div id="example-modal" class="modal hidden">
  <div class="modal-backdrop">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="text-lg font-semibold">Modal Title</h3>
        <button onclick="closeModal('example-modal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <p>Modal content goes here.</p>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" onclick="closeModal('example-modal')">
          Cancel
        </button>
        <button class="btn-primary">Confirm</button>
      </div>
    </div>
  </div>
</div>
```

```css
.modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-content {
  @apply relative bg-white rounded-lg shadow-xl
         max-w-lg w-full mx-4
         transform scale-95 opacity-0
         transition-all duration-300;
}

.modal.modal-open .modal-content {
  @apply scale-100 opacity-100;
}

.modal-header {
  @apply flex items-center justify-between
         px-6 py-4 border-b;
}

.modal-body {
  @apply px-6 py-4;
}

.modal-footer {
  @apply flex justify-end gap-3
         px-6 py-4 border-t;
}
```

#### Utility Functions
```javascript
// Currency Formatting
function formatCurrency(amount, currency = 'KES') {
  const currencyPrefixes = {
    'KES': 'KES ',
    'NGN': '₦',
    'GHS': 'GH₵',
    'TZS': 'TZS ',
    'UGX': 'UGX '
  };

  return currencyPrefixes[currency] + amount.toLocaleString();
}

// Date Formatting
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Copy to Clipboard
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    showToast('Copied to clipboard', 'success');
  }).catch(() => {
    showToast('Failed to copy', 'error');
  });
}

// Debounce Helper
function debounce(func, wait = 300) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Usage with search input
const searchInput = document.getElementById('search');
searchInput.addEventListener('input', debounce((e) => {
  performSearch(e.target.value);
}, 300));
```

---

## Responsive Design

### Breakpoint Strategy

BuyStep follows Tailwind's default breakpoint system with mobile-first approach:

```css
/* Mobile First - Base styles apply to all screens */
.container { padding: 1rem; }

/* Small screens (640px+) - Phones in landscape, small tablets */
@media (min-width: 640px) {
  .sm\:text-lg { font-size: 1.125rem; }
}

/* Medium screens (768px+) - Tablets */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

/* Large screens (1024px+) - Desktops */
@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Extra large screens (1280px+) - Large desktops */
@media (min-width: 1280px) {
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}
```

### Mobile-Specific Patterns

#### Hide on Mobile
```html
<div class="hidden md:block">
  <!-- Only visible on tablets and above -->
</div>
```

#### Mobile-Only
```html
<div class="md:hidden">
  <!-- Only visible on mobile -->
</div>
```

#### Responsive Grid
```html
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  <!-- Adapts from 1 to 4 columns based on screen size -->
</div>
```

#### Responsive Text
```html
<h1 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold">
  <!-- Scales up with screen size -->
</h1>
```

#### Responsive Padding
```html
<section class="py-8 sm:py-12 md:py-16 lg:py-20">
  <!-- More padding on larger screens -->
</section>
```

### Mobile Navigation Patterns

**Desktop**: Fixed sidebar (left) + top navigation bar
**Mobile (<= 768px)**: Top bar + fixed bottom navigation (5 items max)

```css
@media (max-width: 768px) {
  /* Hide sidebar */
  aside { display: none; }

  /* Show mobile bottom nav */
  .mobile-bottom-nav { display: flex; }

  /* Adjust main content for bottom nav */
  main { padding-bottom: 5rem; }
}
```

### Performance Considerations

1. **Image Optimization**
   - Use `loading="lazy"` for images below the fold
   - Provide `width` and `height` attributes to prevent layout shift
   - Use modern formats (WebP) with fallbacks

2. **Font Loading**
   - Use `font-display: swap` for custom fonts
   - Preload critical fonts: `<link rel="preload" as="font">`

3. **Critical CSS**
   - Inline critical above-the-fold CSS
   - Defer non-critical CSS loading

4. **JavaScript**
   - Defer non-critical scripts
   - Use Turbo/Stimulus for progressive enhancement
   - Minimize third-party scripts

---

## Icons & Assets

### Icon System

**Font Awesome 6.4.0** is the primary icon library.

```html
<!-- CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
```

#### Common Icons Used

**Navigation & UI:**
- Home: `fa-home`
- Users/Circles: `fa-users`
- Transactions: `fa-exchange-alt`
- Settings: `fa-cog`
- Notifications: `fa-bell`
- Search: `fa-search`
- Menu: `fa-bars`
- Close: `fa-times`
- Chevron Down: `fa-chevron-down`

**Financial:**
- Piggy Bank: `fa-piggy-bank`
- Wallet: `fa-wallet`
- Chart: `fa-chart-line`
- Money: `fa-money-bill-wave`
- Credit Card: `fa-credit-card`

**Actions:**
- Plus: `fa-plus-circle`
- Edit: `fa-edit`
- Delete: `fa-trash`
- Check: `fa-check-circle`
- Warning: `fa-exclamation-triangle`
- Info: `fa-info-circle`
- Eye: `fa-eye` / `fa-eye-slash`

**Social:**
- WhatsApp: `fab fa-whatsapp`
- Phone: `fa-phone`
- Email: `fa-envelope`

#### Icon Sizes

```html
<!-- Small -->
<i class="fas fa-home text-sm"></i>

<!-- Default -->
<i class="fas fa-home"></i>

<!-- Large -->
<i class="fas fa-home text-xl"></i>

<!-- Extra Large -->
<i class="fas fa-home text-2xl"></i>
```

#### Icon Colors

```html
<!-- Primary -->
<i class="fas fa-check-circle text-primary-600"></i>

<!-- Success -->
<i class="fas fa-check-circle text-green-600"></i>

<!-- Warning -->
<i class="fas fa-exclamation-triangle text-orange-600"></i>

<!-- Error -->
<i class="fas fa-times-circle text-red-600"></i>
```

### Image Guidelines

**Hero Images:**
- Format: SVG or WebP with PNG fallback
- Max size: 500KB
- Dimensions: 1200x800px minimum
- Alt text required for accessibility

**Profile Avatars:**
- Format: WebP with JPEG fallback
- Size: 128x128px, 256x256px, 512x512px (responsive)
- Circular crop
- Default avatar for users without photos

**Illustrations:**
- Prefer SVG for scalability
- Use BuyStep color palette (primary, secondary, accent)
- Optimize SVG code (remove unnecessary elements)

---

## Rails Integration

### Tailwind CSS v4 Setup

**Install Tailwind CSS with Rails:**

```bash
# Add required gems
bundle add tailwindcss-ruby
bundle add tailwindcss-rails

# Install Tailwind
./bin/rails tailwindcss:install

# Start development server
./bin/dev
```

**Configure Tailwind v4 with @theme (`app/assets/stylesheets/application.tailwind.css`):**

```css
@import "tailwindcss";

/* BuyStep Design System Configuration */
@theme {
  /* Primary Colors (Green - African Growth) */
  --color-primary-50: #f0fdf4;
  --color-primary-100: #dcfce7;
  --color-primary-200: #bbf7d0;
  --color-primary-300: #86efac;
  --color-primary-400: #4ade80;
  --color-primary-500: #22c55e;
  --color-primary-600: #16a34a;
  --color-primary-700: #15803d;
  --color-primary-800: #166534;
  --color-primary-900: #14532d;
  --color-primary-950: #052e16;

  /* Secondary Colors (Orange - Energy & Warmth) */
  --color-secondary-50: #fff7ed;
  --color-secondary-100: #ffedd5;
  --color-secondary-200: #fed7aa;
  --color-secondary-300: #fdba74;
  --color-secondary-400: #fb923c;
  --color-secondary-500: #f97316;
  --color-secondary-600: #ea580c;
  --color-secondary-700: #c2410c;
  --color-secondary-800: #9a3412;
  --color-secondary-900: #7c2d12;
  --color-secondary-950: #431407;

  /* Accent Colors (Purple - Modern Innovation) */
  --color-accent-50: #faf5ff;
  --color-accent-100: #f3e8ff;
  --color-accent-200: #e9d5ff;
  --color-accent-300: #d8b4fe;
  --color-accent-400: #c084fc;
  --color-accent-500: #8b5cf6;
  --color-accent-600: #7c3aed;
  --color-accent-700: #6d28d9;
  --color-accent-800: #5b21b6;
  --color-accent-900: #4c1d95;
  --color-accent-950: #2e1065;

  /* Typography */
  --font-sans: 'Inter', sans-serif;
  --font-display: 'Montserrat', sans-serif;
}

/* Custom Component Classes */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800
           text-white font-medium
           px-6 py-3 rounded-lg
           transition-all duration-200
           shadow-md hover:shadow-lg
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply border-2 border-primary-600 text-primary-600
           hover:bg-primary-600 hover:text-white
           font-medium px-6 py-3 rounded-lg
           transition-all duration-200
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-accent {
    @apply bg-accent-600 hover:bg-accent-700 active:bg-accent-800
           text-white font-medium
           px-6 py-3 rounded-lg
           transition-all duration-200
           shadow-md hover:shadow-lg
           focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg
           focus:ring-2 focus:ring-primary-500 focus:border-primary-500
           transition-all duration-200
           placeholder:text-gray-400;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-red-500 text-sm mt-1;
  }

  .card {
    @apply bg-white rounded-xl shadow-md overflow-hidden
           transition-all duration-200
           hover:shadow-xl hover:-translate-y-1;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-title {
    @apply text-lg font-semibold text-gray-800;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
  }

  .icon-circle {
    @apply bg-primary-100 text-primary-600
           w-16 h-16 rounded-full
           flex items-center justify-center;
  }

  .alert {
    @apply px-4 py-3 rounded-lg flex items-center gap-3 mb-4;
  }

  .alert-success {
    @apply bg-green-50 text-green-800 border border-green-200;
  }

  .alert-warning {
    @apply bg-orange-50 text-orange-800 border border-orange-200;
  }

  .alert-error {
    @apply bg-red-50 text-red-800 border border-red-200;
  }

  .alert-info {
    @apply bg-blue-50 text-blue-800 border border-blue-200;
  }

  .badge {
    @apply inline-flex items-center px-3 py-1 rounded-full
           text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }

  .badge-accent {
    @apply bg-accent-100 text-accent-800;
  }

  .nav-link {
    @apply text-gray-700 hover:text-primary-600
           font-medium transition-colors duration-200;
  }

  .nav-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-lg
           text-gray-700 hover:bg-gray-100 hover:text-primary-600
           transition-all duration-200 font-medium;
  }

  .nav-item.active {
    @apply bg-primary-600 text-white hover:bg-primary-700 hover:text-white;
  }

  .dropdown-menu {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg
           border border-gray-200 py-2 z-50;
  }

  .dropdown-item {
    @apply flex items-center gap-3 px-4 py-2
           text-gray-700 hover:bg-gray-100
           transition-colors duration-150;
  }
}

/* Custom Animations & Utilities */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
  }
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
  display: none;
}

@media (max-width: 768px) {
  .mobile-bottom-nav {
    @apply flex fixed bottom-0 left-0 right-0
           bg-white border-t border-gray-200 shadow-lg
           justify-around items-center h-16 z-50;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center gap-1
           text-gray-600 hover:text-primary-600
           transition-colors duration-200 flex-1;
  }

  .mobile-nav-item.active {
    @apply text-primary-600;
  }
}
```

**Key Tailwind v4 Features:**
- ✅ **No `tailwind.config.js`** - Everything configured in CSS
- ✅ **@theme directive** - Define colors, fonts, spacing using CSS custom properties
- ✅ **Automatic content detection** - No need to specify template paths
- ✅ **Native CSS variables** - Direct browser integration
- ✅ **Simpler setup** - Fewer files, clearer configuration

### Font Loading

**Add Google Fonts to layout (`app/views/layouts/application.html.erb`):**

```erb
<head>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Montserrat:wght@600;700;800&display=swap" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <%= javascript_importmap_tags %>
</head>
```

### Hotwire/Turbo Integration

**Enable Turbo for navigation:**

All navigation should work with Turbo Drive by default. For interactive components, use Stimulus controllers.

**Example Stimulus Controller (`app/javascript/controllers/dropdown_controller.js`):**

```javascript
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["menu"]

  toggle() {
    this.menuTarget.classList.toggle('hidden')
  }

  hide(event) {
    if (!this.element.contains(event.target)) {
      this.menuTarget.classList.add('hidden')
    }
  }
}
```

**Usage in view:**

```erb
<div data-controller="dropdown" data-action="click@window->dropdown#hide">
  <button data-action="dropdown#toggle">
    User Menu
    <i class="fas fa-chevron-down"></i>
  </button>

  <div data-dropdown-target="menu" class="dropdown-menu hidden">
    <%= link_to "Profile", profile_path, class: "dropdown-item" %>
    <%= link_to "Settings", settings_path, class: "dropdown-item" %>
    <%= button_to "Logout", logout_path, method: :delete, class: "dropdown-item" %>
  </div>
</div>
```

### View Helpers

**Create custom helpers (`app/helpers/ui_helper.rb`):**

```ruby
module UiHelper
  def icon_tag(icon_class, size: nil, color: nil)
    css_classes = ["fas", icon_class]
    css_classes << "text-#{size}" if size
    css_classes << "text-#{color}" if color

    content_tag(:i, nil, class: css_classes.join(' '))
  end

  def badge(text, variant: :primary)
    content_tag(:span, text, class: "badge badge-#{variant}")
  end

  def alert_box(message, type: :info, dismissible: true)
    content_tag(:div, class: "alert alert-#{type}") do
      concat icon_tag('fa-info-circle')
      concat content_tag(:span, message)
      if dismissible
        concat button_tag(icon_tag('fa-times'),
                         class: 'ml-auto',
                         data: { action: 'click->alert#dismiss' })
      end
    end
  end
end
```

**Usage in views:**

```erb
<%= icon_tag 'fa-check-circle', size: 'xl', color: 'green-600' %>
<%= badge 'Active', variant: :primary %>
<%= alert_box 'Settings saved successfully!', type: :success %>
```

### Responsive Images

**Use Rails image helpers with responsive attributes:**

```erb
<%= image_tag "hero.jpg",
    alt: "BuyStep Hero",
    loading: "lazy",
    class: "w-full h-auto rounded-lg shadow-xl",
    width: 1200,
    height: 800 %>
```

### Component Partials

**Create reusable partials (`app/views/shared/_card.html.erb`):**

```erb
<div class="card <%= local_assigns[:extra_classes] %>">
  <% if local_assigns[:icon] %>
    <div class="card-body text-center">
      <div class="icon-circle mx-auto mb-4">
        <%= icon_tag icon, size: '2xl' %>
      </div>
      <h3 class="text-xl font-semibold mb-2"><%= title %></h3>
      <p class="text-gray-600"><%= description %></p>
    </div>
  <% else %>
    <div class="card-header">
      <h3 class="card-title"><%= title %></h3>
    </div>
    <div class="card-body">
      <%= yield %>
    </div>
  <% end %>
</div>
```

**Usage:**

```erb
<%= render 'shared/card',
    title: 'Join Circles',
    description: 'Connect with others to save together',
    icon: 'fa-users' %>

<%= render 'shared/card', title: 'Transaction History' do %>
  <!-- Custom content -->
  <table class="table">
    <!-- ... -->
  </table>
<% end %>
```

---

## Appendix

### Color Palette Quick Reference

| Color | Hex Code | Usage |
|-------|----------|-------|
| Primary 500 | `#22c55e` | Buttons, links, brand |
| Primary 600 | `#16a34a` | Hover states |
| Secondary 500 | `#f97316` | Secondary CTAs |
| Accent 500 | `#8b5cf6` | Premium features |
| Gray 800 | `#1f2937` | Primary text |
| Gray 600 | `#4b5563` | Body text |
| Gray 400 | `#9ca3af` | Placeholder text |

### Checklist for New Pages

- [ ] Mobile-first responsive design implemented
- [ ] All interactive elements keyboard accessible
- [ ] Form validation with inline error messages
- [ ] Toast notifications for user feedback
- [ ] Loading states for async operations
- [ ] Empty states with helpful guidance
- [ ] Error states with recovery actions
- [ ] Success states with next steps
- [ ] Consistent navigation patterns
- [ ] Page meta tags (title, description)
- [ ] Open Graph tags for social sharing
- [ ] Proper semantic HTML structure
- [ ] Alt text for all images
- [ ] ARIA labels for icon-only buttons
- [ ] Focus states visible on all interactive elements

### Resources

**Design Tools:**
- Figma: https://figma.com
- Tailwind UI Components: https://tailwindui.com
- Heroicons: https://heroicons.com
- Font Awesome: https://fontawesome.com

**Tailwind CSS v4:**
- Official Docs: https://tailwindcss.com/docs
- v4 Announcement: https://tailwindcss.com/blog/tailwindcss-v4
- Theme Configuration: https://tailwindcss.com/docs/theme
- Rails Guide: https://tailwindcss.com/docs/installation/framework-guides/ruby-on-rails

**Rails Resources:**
- Tailwind CSS Rails: https://github.com/rails/tailwindcss-rails
- Hotwire Turbo: https://turbo.hotwired.dev
- Stimulus: https://stimulus.hotwired.dev
- ViewComponent: https://viewcomponent.org

**Accessibility:**
- WCAG 2.1 Guidelines: https://www.w3.org/WAI/WCAG21/quickref/
- ARIA Authoring Practices: https://www.w3.org/WAI/ARIA/apg/

---

**End of Design Guide**

*This document uses Tailwind CSS v4 with CSS-first configuration. For questions or clarifications, refer to the buystep_ui reference implementation and Tailwind CSS v4 documentation.*
