# Buystep Commerce - Development Guide 🚀

> **Rails 8 Native Stack | TDD-First | Git Best Practices**

## Table of Contents
- [Quick Start](#quick-start)
- [Tech Stack](#tech-stack)
- [Core Principles](#core-principles)
- [TDD Workflow](#tdd-workflow)
- [Git Workflow](#git-workflow)
- [Rails 8 Configuration](#rails-8-configuration)
- [Domain Implementation Guide](#domain-implementation-guide)
- [Testing Strategy](#testing-strategy)
- [Common Patterns](#common-patterns)
- [Development Commands](#development-commands)
- [Troubleshooting](#troubleshooting)

---

## Quick Start

### Prerequisites
```bash
✓ Ruby 3.3.0
✓ Rails 8.0+
✓ PostgreSQL 16+
✓ Node.js 20+
✓ Yarn
```

### Setup (20 minutes)
```bash
# Clone and setup
git clone <repository-url>
cd buystep
bundle install
yarn install

# Database setup
rails db:create db:migrate db:seed

# Start development
bin/dev
```

Visit: http://localhost:3000

**Default Credentials**:
- Admin: `<EMAIL>` / OTP
- Test User: `<EMAIL>` / OTP

---

## Tech Stack

### Rails 8 Native Stack (No Redis! No Sidekiq!)

| Component | Solution | Purpose |
|-----------|----------|---------|
| **Backend** | Rails 8.0+ | Web framework |
| **Database** | PostgreSQL 16+ | Single source of truth |
| **Caching** | Solid Cache | Database-backed caching |
| **Jobs** | Solid Queue | Database-backed background jobs |
| **WebSockets** | Solid Cable | Database-backed real-time |
| **Frontend** | Hotwire (Turbo + Stimulus) | Progressive enhancement |
| **CSS** | TailwindCSS 3+ | Utility-first styling |
| **Auth** | Devise Passwordless | OTP/Magic link authentication |
| **Authorization** | Pundit | Policy-based access control |
| **Money** | Money-Rails | Multi-currency support |
| **Testing** | RSpec + Factory Bot | Test framework |

### Why No Redis?
- **Simpler**: Single database backup/restore
- **Cheaper**: Lower infrastructure costs
- **Reliable**: No Redis memory issues
- **Better for Africa**: Less complex infrastructure
- **Rails 8 Way**: Use built-in solutions

---

## Core Principles

### 1. TDD is Non-Negotiable
```ruby
# RED → GREEN → REFACTOR (Always!)

# ❌ WRONG: Write code first
def create_circle
  Circle.create!(params)  # No tests!
end

# ✅ CORRECT: Write test first
# spec/services/circles/creation_service_spec.rb
RSpec.describe Circles::CreationService do
  describe '#call' do
    it 'creates circle with valid params' do
      # RED: Write this test first
      # GREEN: Make it pass
      # REFACTOR: Clean up
    end
  end
end
```

**Coverage Requirement**: ≥90% (enforced in CI)

### 2. Git Workflow is Mandatory
```bash
# Every task = New branch → Test → Commit → Push → PR

# ❌ WRONG: Commit directly to main/dev
git checkout main
git commit -am "add feature"  # BLOCKED!

# ✅ CORRECT: Branch → PR workflow
git checkout dev
git checkout -b feature/circle-voting
# [Write tests → Implement → Test pass]
git commit -m "feat(circles): add voting system"
git push origin feature/circle-voting
gh pr create --base dev
```

### 3. Semantic Commits
```bash
# Format: type(scope): subject

feat(circles): add member voting system
fix(payments): resolve M-Pesa timeout issue
test(credit): add credit scoring edge cases
docs(readme): update installation steps
refactor(services): extract payment logic
chore(deps): update rails to 8.0.1
```

**Types**: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

---

## TDD Workflow

### The Red-Green-Refactor Cycle

#### Step 1: RED (Write Failing Test)
```ruby
# spec/domains/circles/services/voting_service_spec.rb
RSpec.describe Circles::VotingService do
  describe '#cast_vote' do
    subject(:service) { described_class.new(membership, proposal) }

    let(:circle) { create(:circle) }
    let(:membership) { create(:membership, circle: circle) }
    let(:proposal) { create(:proposal, circle: circle) }

    context 'when member is eligible to vote' do
      it 'creates a vote record' do
        expect {
          service.cast_vote(:approve)
        }.to change(Vote, :count).by(1)
      end

      it 'marks proposal as approved when threshold reached' do
        # Setup: 5 members, need 3 votes
        create_list(:membership, 4, circle: circle)

        service.cast_vote(:approve)

        expect(proposal.reload.status).to eq('approved')
      end
    end
  end
end

# Run test
# $ bundle exec rspec spec/domains/circles/services/voting_service_spec.rb
# Result: FAILED (expected - no implementation yet!)
```

#### Step 2: GREEN (Make it Pass)
```ruby
# app/domains/circles/services/voting_service.rb
module Circles
  class VotingService
    def initialize(membership, proposal)
      @membership = membership
      @proposal = proposal
      @circle = membership.circle
    end

    def cast_vote(vote_type)
      ActiveRecord::Base.transaction do
        vote = create_vote(vote_type)
        check_and_update_proposal_status
        vote
      end
    end

    private

    def create_vote(vote_type)
      @membership.votes.create!(
        proposal: @proposal,
        vote_type: vote_type,
        cast_at: Time.current
      )
    end

    def check_and_update_proposal_status
      approval_threshold = (@circle.members_count * 0.6).ceil
      approvals = @proposal.votes.where(vote_type: :approve).count

      if approvals >= approval_threshold
        @proposal.update!(status: :approved)
      end
    end
  end
end

# Run test
# $ bundle exec rspec spec/domains/circles/services/voting_service_spec.rb
# Result: PASSED ✅
```

#### Step 3: REFACTOR (Clean Up)
```ruby
# Extract threshold calculation to model
class Circle < ApplicationRecord
  def approval_threshold
    (members_count * 0.6).ceil
  end
end

# Simplify service
def check_and_update_proposal_status
  if @proposal.votes.where(vote_type: :approve).count >= @circle.approval_threshold
    @proposal.update!(status: :approved)
  end
end

# Run tests again
# $ bundle exec rspec
# Result: Still PASSED ✅
```

### Testing Pyramid

```
         /\
        /E2E\      10% - System tests (Critical user flows)
       /------\
      /  Integ \   20% - Integration tests (Controllers, APIs)
     /----------\
    /    Unit    \ 70% - Unit tests (Models, Services, Jobs)
   /--------------\
```

---

## Git Workflow

### Branch Strategy

```
main (production)
  └── dev (development)
       ├── feature/circle-voting
       ├── feature/passwordless-auth
       ├── bugfix/payment-timeout
       └── hotfix/security-patch
```

### Complete Feature Workflow

```bash
# 1. Start from dev branch
git checkout dev
git pull origin dev

# 2. Create feature branch
git checkout -b feature/circle-voting

# 3. Write failing tests (RED)
touch spec/domains/circles/services/voting_service_spec.rb
bundle exec rspec spec/domains/circles/services/voting_service_spec.rb
# Expected: FAILED ❌

# 4. Implement feature (GREEN)
touch app/domains/circles/services/voting_service.rb
# [Write implementation]
bundle exec rspec spec/domains/circles/services/voting_service_spec.rb
# Expected: PASSED ✅

# 5. Run full test suite
bundle exec rspec
# All tests passing ✅

# 6. Check code quality
bundle exec rubocop
# No violations ✅

# 7. Check test coverage
COVERAGE=true bundle exec rspec
# Coverage: 92% ✅

# 8. Commit with semantic message
git add .
git commit -m "feat(circles): implement voting system for member decisions

- Add VotingService for proposal voting
- Implement vote tallying and approval threshold
- Add Vote model with validations
- Add Pundit policies for voting authorization
- Test coverage: 92%

Closes #45"

# 9. Push to remote
git push origin feature/circle-voting

# 10. Create Pull Request
gh pr create \
  --base dev \
  --title "Add Circle Voting System" \
  --body "## Description
Implements member voting system for circle decisions.

## Changes
- New VotingService for casting votes
- Vote model with associations
- Authorization policies
- Comprehensive test coverage

## Testing
- All tests passing ✅
- Coverage: 92% ✅
- RuboCop clean ✅

## Screenshots
[Add screenshots if UI changes]"

# 11. Wait for CI/CD checks
# - RSpec: ✅
# - RuboCop: ✅
# - Coverage: ✅

# 12. Code Review
# - Approve from team member required

# 13. Merge to dev
git checkout dev
git pull origin dev
git merge --no-ff feature/circle-voting
git push origin dev

# 14. Delete feature branch
git branch -d feature/circle-voting
git push origin --delete feature/circle-voting

# 15. Test on dev environment
# [Manual testing on staging/dev server]

# 16. When stable, merge dev to main
git checkout main
git merge --no-ff dev
git push origin main
```

### PR Requirements Checklist

Every PR must have:
- [ ] Clear description of changes
- [ ] Link to related issue (`Closes #123`)
- [ ] All tests passing (≥90% coverage)
- [ ] RuboCop violations resolved
- [ ] Screenshots (if UI changes)
- [ ] Breaking changes documented
- [ ] Database migrations reviewed
- [ ] At least 1 approval

---

## Rails 8 Configuration

### Solid Cache (Database-Backed Caching)

```ruby
# config/environments/production.rb
config.cache_store = :solid_cache_store

# Benefits:
# - 10TB+ capacity (vs 2GB Redis typical)
# - No memory constraints
# - Automatic encryption
# - 60-day retention policies
# - No Redis to manage!

# Usage in code
class ExpensiveOperation
  def calculate
    Rails.cache.fetch("expensive_calc/#{id}", expires_in: 1.hour) do
      # Expensive calculation
      perform_complex_calculation
    end
  end
end
```

#### Testing Solid Cache
```ruby
# spec/services/credit/scoring_service_spec.rb
RSpec.describe Credit::ScoringService do
  describe '#calculate_score' do
    let(:user) { create(:user) }
    let(:service) { described_class.new(user) }

    it 'caches result for 1 hour' do
      expect(Rails.cache).to receive(:fetch)
        .with("credit_score/#{user.id}", expires_in: 1.hour)
        .and_call_original

      service.calculate_score
    end

    it 'returns cached result on subsequent calls' do
      first_call = service.calculate_score

      # Mock expensive calculation
      allow(service).to receive(:perform_calculation).and_return(99)

      second_call = service.calculate_score

      expect(second_call).to eq(first_call)
    end
  end
end
```

### Solid Queue (Database-Backed Background Jobs)

```ruby
# config/queue.yml
production:
  dispatchers:
    - polling_interval: 1
      batch_size: 500

  workers:
    - queues: critical,default,low
      threads: 5
      processes: 3
      polling_interval: 0.1

# app/jobs/application_job.rb
class ApplicationJob < ActiveJob::Base
  # Automatic retries with exponential backoff
  retry_on StandardError, wait: :exponentially_longer, attempts: 5

  # Don't retry certain errors
  discard_on ActiveRecord::RecordNotFound
  discard_on ActiveRecord::RecordInvalid

  # Default queue
  queue_as :default

  # Concurrency control (Solid Queue feature!)
  # limits_concurrency to: 5, key: -> { arguments.first }
end
```

#### Queue Priority Strategy
```ruby
# Critical: Time-sensitive operations
class SendOtpJob < ApplicationJob
  queue_as :critical

  def perform(user_id, otp_code)
    user = User.find(user_id)
    SmsService.send_otp(user.phone_number, otp_code)
  end
end

# Default: Regular operations
class ProcessPaymentJob < ApplicationJob
  queue_as :default

  def perform(payment_id)
    payment = Payment.find(payment_id)
    PaymentService.process(payment)
  end
end

# Low: Non-urgent operations
class GenerateReportJob < ApplicationJob
  queue_as :low

  def perform(report_id)
    report = Report.find(report_id)
    ReportGenerator.generate(report)
  end
end
```

#### Recurring Jobs (Built-in Cron!)
```ruby
class DailyReportJob < ApplicationJob
  # Runs every day at 9 AM
  recurring schedule: "0 9 * * *"

  def perform
    Report.generate_daily
  end
end

class WeeklySummaryJob < ApplicationJob
  # Runs every Monday at 8 AM
  recurring schedule: "0 8 * * MON"

  def perform
    WeeklySummary.send_to_all_users
  end
end
```

#### Testing Solid Queue Jobs
```ruby
# spec/jobs/circles/process_contribution_job_spec.rb
RSpec.describe Circles::ProcessContributionJob, type: :job do
  let(:contribution) { create(:contribution, status: :pending) }

  describe '#perform' do
    it 'processes contribution successfully' do
      described_class.perform_now(contribution.id)

      expect(contribution.reload).to have_attributes(
        status: 'completed',
        processed_at: be_present
      )
    end

    it 'enqueues job on correct queue' do
      expect {
        described_class.perform_later(contribution.id)
      }.to have_enqueued_job(described_class)
        .on_queue('critical')
        .with(contribution.id)
    end

    it 'retries on failure' do
      allow(Circles::ContributionService).to receive(:process)
        .and_raise(StandardError)

      expect {
        described_class.perform_now(contribution.id)
      }.to raise_error(StandardError)

      # Job will be retried automatically
    end
  end
end
```

### Solid Cable (Database-Backed WebSockets)

```ruby
# config/cable.yml
production:
  adapter: solid_cable

# No Redis needed for ActionCable!
# Messages stored in DB for debugging

# app/channels/circle_channel.rb
class CircleChannel < ApplicationCable::Channel
  def subscribed
    circle = Circle.find(params[:circle_id])
    stream_for circle
  end

  def receive(data)
    # Handle incoming messages
  end
end

# Broadcasting from anywhere
CircleChannel.broadcast_to(
  circle,
  { event: 'member_joined', member: member.as_json }
)
```

### Devise Passwordless Authentication

```ruby
# Gemfile
gem 'devise'
gem 'devise-passwordless'

# config/initializers/devise.rb
Devise.setup do |config|
  config.passwordless_login_within = 20.minutes
  config.passwordless_secret_key = Rails.application.credentials.dig(:devise, :passwordless_key)
end

# app/models/user.rb
class User < ApplicationRecord
  # Use OTP authentication (no passwords!)
  devise :passwordless_authenticatable, :trackable

  # Generate 6-digit OTP
  def generate_otp
    self.otp_code = rand(100000..999999).to_s
    self.otp_sent_at = Time.current
    save!
  end

  # Send OTP via SMS
  def send_otp
    generate_otp
    SmsService.send(
      phone_number: phone_number,
      message: "Your Buystep OTP: #{otp_code}. Valid for 20 minutes."
    )
  end

  # Verify OTP
  def verify_otp(code)
    return false if otp_sent_at < 20.minutes.ago
    return false unless code == otp_code

    # Clear OTP after successful verification
    update(otp_code: nil, otp_sent_at: nil)
    true
  end
end
```

#### Passwordless Authentication Flow
```ruby
# app/controllers/sessions_controller.rb
class SessionsController < Devise::SessionsController
  # Step 1: Request OTP
  def request_otp
    user = User.find_by(phone_number: params[:phone_number])

    if user
      user.send_otp
      render json: {
        status: 'otp_sent',
        message: 'OTP sent to your phone'
      }
    else
      render json: {
        error: 'Phone number not found'
      }, status: :not_found
    end
  end

  # Step 2: Verify OTP
  def verify_otp
    user = User.find_by(phone_number: params[:phone_number])

    if user&.verify_otp(params[:otp])
      sign_in user
      render json: {
        status: 'authenticated',
        user: user.as_json(only: [:id, :phone_number, :email])
      }
    else
      render json: {
        error: 'Invalid or expired OTP'
      }, status: :unauthorized
    end
  end
end
```

#### Testing Passwordless Auth
```ruby
# spec/features/passwordless_authentication_spec.rb
RSpec.describe 'Passwordless Authentication', type: :system do
  let(:user) { create(:user, phone_number: '+254700000001') }

  it 'allows login with OTP' do
    visit new_user_session_path

    # Step 1: Enter phone number
    fill_in 'Phone number', with: user.phone_number
    click_button 'Send OTP'

    expect(page).to have_content('OTP sent')

    # Step 2: Enter OTP
    otp = user.reload.otp_code
    fill_in 'OTP', with: otp
    click_button 'Verify'

    # Step 3: Authenticated
    expect(page).to have_content('Welcome')
    expect(current_path).to eq(dashboard_path)
  end

  it 'rejects invalid OTP' do
    visit new_user_session_path

    fill_in 'Phone number', with: user.phone_number
    click_button 'Send OTP'

    fill_in 'OTP', with: '000000'  # Wrong OTP
    click_button 'Verify'

    expect(page).to have_content('Invalid or expired OTP')
  end

  it 'expires OTP after 20 minutes' do
    user.generate_otp

    travel 21.minutes do
      expect(user.verify_otp(user.otp_code)).to be false
    end
  end
end
```

---

## Domain Implementation Guide

### TDD Approach for New Features

Let's implement a complete feature: **Circle Voting System**

#### 1. Write Model Tests (RED)
```ruby
# spec/domains/circles/models/vote_spec.rb
RSpec.describe Circles::Vote, type: :model do
  describe 'associations' do
    it { should belong_to(:membership) }
    it { should belong_to(:proposal) }
  end

  describe 'validations' do
    it { should validate_presence_of(:vote_type) }
    it { should define_enum_for(:vote_type).with_values(approve: 0, reject: 1, abstain: 2) }
  end

  describe 'scopes' do
    let(:proposal) { create(:proposal) }

    it 'filters by vote type' do
      approve_vote = create(:vote, proposal: proposal, vote_type: :approve)
      reject_vote = create(:vote, proposal: proposal, vote_type: :reject)

      expect(described_class.approved).to include(approve_vote)
      expect(described_class.approved).not_to include(reject_vote)
    end
  end
end

# Run test
# $ bundle exec rspec spec/domains/circles/models/vote_spec.rb
# Result: FAILED ❌ (model doesn't exist yet)
```

#### 2. Create Migration (GREEN)
```bash
rails generate migration CreateCirclesVotes \
  membership:references \
  proposal:references \
  vote_type:integer \
  cast_at:datetime

# Edit migration
class CreateCirclesVotes < ActiveRecord::Migration[8.0]
  def change
    create_table :circles_votes do |t|
      t.references :membership, null: false, foreign_key: { to_table: :circles_memberships }
      t.references :proposal, null: false, foreign_key: { to_table: :circles_proposals }
      t.integer :vote_type, null: false, default: 0
      t.datetime :cast_at, null: false

      t.timestamps
    end

    add_index :circles_votes, [:membership_id, :proposal_id], unique: true
  end
end

rails db:migrate
```

#### 3. Create Model (GREEN)
```ruby
# app/domains/circles/models/vote.rb
module Circles
  class Vote < ApplicationRecord
    self.table_name = 'circles_votes'

    # Associations
    belongs_to :membership, class_name: 'Circles::Membership'
    belongs_to :proposal, class_name: 'Circles::Proposal'

    # Enums
    enum vote_type: { approve: 0, reject: 1, abstain: 2 }

    # Validations
    validates :vote_type, presence: true
    validates :membership_id, uniqueness: { scope: :proposal_id }
    validates :cast_at, presence: true

    # Scopes
    scope :approved, -> { where(vote_type: :approve) }
    scope :rejected, -> { where(vote_type: :reject) }
  end
end

# Run test
# $ bundle exec rspec spec/domains/circles/models/vote_spec.rb
# Result: PASSED ✅
```

#### 4. Write Service Tests (RED)
```ruby
# spec/domains/circles/services/voting_service_spec.rb
RSpec.describe Circles::VotingService, type: :service do
  subject(:service) { described_class.new(membership, proposal) }

  let(:circle) { create(:circle, members_count: 5) }
  let(:membership) { create(:membership, circle: circle) }
  let(:proposal) { create(:proposal, circle: circle, status: :pending) }

  describe '#cast_vote' do
    context 'when member is eligible' do
      it 'creates vote record' do
        expect {
          service.cast_vote(:approve)
        }.to change(Circles::Vote, :count).by(1)
      end

      it 'marks proposal as approved when threshold reached' do
        # Need 3 votes (60% of 5 members)
        create(:vote, proposal: proposal, vote_type: :approve)
        create(:vote, proposal: proposal, vote_type: :approve)

        service.cast_vote(:approve)

        expect(proposal.reload.status).to eq('approved')
      end

      it 'enqueues notification job' do
        expect {
          service.cast_vote(:approve)
        }.to have_enqueued_job(Circles::VoteNotificationJob)
      end
    end

    context 'when member already voted' do
      before { create(:vote, membership: membership, proposal: proposal) }

      it 'raises error' do
        expect {
          service.cast_vote(:approve)
        }.to raise_error(Circles::VotingService::AlreadyVotedError)
      end
    end
  end
end

# Run test
# $ bundle exec rspec spec/domains/circles/services/voting_service_spec.rb
# Result: FAILED ❌ (service doesn't exist yet)
```

#### 5. Implement Service (GREEN)
```ruby
# app/domains/circles/services/voting_service.rb
module Circles
  class VotingService
    class AlreadyVotedError < StandardError; end

    def initialize(membership, proposal)
      @membership = membership
      @proposal = proposal
      @circle = membership.circle
    end

    def cast_vote(vote_type)
      raise AlreadyVotedError if already_voted?

      ActiveRecord::Base.transaction do
        vote = create_vote(vote_type)
        check_and_update_proposal
        notify_circle_members
        vote
      end
    end

    private

    def already_voted?
      @proposal.votes.exists?(membership: @membership)
    end

    def create_vote(vote_type)
      @membership.votes.create!(
        proposal: @proposal,
        vote_type: vote_type,
        cast_at: Time.current
      )
    end

    def check_and_update_proposal
      return unless approval_threshold_reached?

      @proposal.update!(status: :approved, approved_at: Time.current)
    end

    def approval_threshold_reached?
      approvals = @proposal.votes.approved.count
      approvals >= @circle.approval_threshold
    end

    def notify_circle_members
      Circles::VoteNotificationJob.perform_later(@proposal.id, @membership.id)
    end
  end
end

# Run test
# $ bundle exec rspec spec/domains/circles/services/voting_service_spec.rb
# Result: PASSED ✅
```

#### 6. Write Job Tests (RED)
```ruby
# spec/domains/circles/jobs/vote_notification_job_spec.rb
RSpec.describe Circles::VoteNotificationJob, type: :job do
  let(:proposal) { create(:proposal) }
  let(:membership) { create(:membership) }

  it 'sends notifications to circle members' do
    expect(NotificationService).to receive(:notify_vote_cast)
      .with(proposal, membership)

    described_class.perform_now(proposal.id, membership.id)
  end

  it 'enqueues on default queue' do
    expect {
      described_class.perform_later(proposal.id, membership.id)
    }.to have_enqueued_job(described_class)
      .on_queue('default')
  end
end
```

#### 7. Implement Job (GREEN)
```ruby
# app/domains/circles/jobs/vote_notification_job.rb
module Circles
  class VoteNotificationJob < ApplicationJob
    queue_as :default

    def perform(proposal_id, membership_id)
      proposal = Proposal.find(proposal_id)
      membership = Membership.find(membership_id)

      NotificationService.notify_vote_cast(proposal, membership)
    end
  end
end
```

#### 8. Write Controller Tests (RED)
```ruby
# spec/requests/circles/votes_spec.rb
RSpec.describe 'Circles::Votes', type: :request do
  let(:user) { create(:user) }
  let(:circle) { create(:circle) }
  let(:membership) { create(:membership, user: user, circle: circle) }
  let(:proposal) { create(:proposal, circle: circle) }

  before { sign_in user }

  describe 'POST /circles/:circle_id/proposals/:proposal_id/votes' do
    let(:valid_params) { { vote: { vote_type: 'approve' } } }

    it 'creates vote' do
      expect {
        post circle_proposal_votes_path(circle, proposal), params: valid_params
      }.to change(Circles::Vote, :count).by(1)
    end

    it 'returns success response' do
      post circle_proposal_votes_path(circle, proposal), params: valid_params

      expect(response).to have_http_status(:created)
      expect(json_response['status']).to eq('voted')
    end

    it 'returns error when already voted' do
      create(:vote, membership: membership, proposal: proposal)

      post circle_proposal_votes_path(circle, proposal), params: valid_params

      expect(response).to have_http_status(:unprocessable_entity)
    end
  end
end
```

#### 9. Implement Controller (GREEN)
```ruby
# app/controllers/circles/votes_controller.rb
module Circles
  class VotesController < ApplicationController
    before_action :authenticate_user!
    before_action :set_circle
    before_action :set_proposal
    before_action :set_membership

    def create
      service = VotingService.new(@membership, @proposal)
      vote = service.cast_vote(vote_params[:vote_type])

      render json: {
        status: 'voted',
        vote: vote.as_json
      }, status: :created
    rescue VotingService::AlreadyVotedError
      render json: {
        error: 'You have already voted on this proposal'
      }, status: :unprocessable_entity
    end

    private

    def set_circle
      @circle = Circle.find(params[:circle_id])
    end

    def set_proposal
      @proposal = @circle.proposals.find(params[:proposal_id])
    end

    def set_membership
      @membership = @circle.memberships.find_by!(user: current_user)
    end

    def vote_params
      params.require(:vote).permit(:vote_type)
    end
  end
end
```

#### 10. Write System Test (E2E)
```ruby
# spec/system/circles/voting_spec.rb
RSpec.describe 'Circle Voting', type: :system do
  let(:user) { create(:user) }
  let(:circle) { create(:circle) }
  let(:membership) { create(:membership, user: user, circle: circle) }
  let(:proposal) { create(:proposal, circle: circle) }

  before do
    sign_in user
    visit circle_proposal_path(circle, proposal)
  end

  it 'allows member to vote on proposal' do
    within '#voting-section' do
      click_button 'Approve'
    end

    expect(page).to have_content('Your vote has been recorded')
    expect(page).to have_content('Votes: 1')
  end

  it 'shows approval status when threshold reached', js: true do
    # Setup: Add votes to reach threshold
    create_list(:vote, 2, proposal: proposal, vote_type: :approve)

    visit circle_proposal_path(circle, proposal)

    within '#voting-section' do
      click_button 'Approve'
    end

    expect(page).to have_content('Proposal Approved')
  end
end
```

#### 11. Run Full Test Suite
```bash
bundle exec rspec

# All tests passing ✅
# Coverage: 92% ✅
```

#### 12. Commit and Push
```bash
git add .
git commit -m "feat(circles): implement voting system for proposals

- Add Vote model with associations and validations
- Implement VotingService for vote casting and tallying
- Add authorization policies for voting
- Create VoteNotificationJob for member notifications
- Add votes controller with create endpoint
- Add system tests for voting flow
- Test coverage: 92%

Closes #45"

git push origin feature/circle-voting
```

---

## Testing Strategy

### Unit Tests (70% of tests)
```ruby
# Models
RSpec.describe Circle, type: :model do
  # Test associations
  # Test validations
  # Test scopes
  # Test instance methods
  # Test callbacks
end

# Services
RSpec.describe Circles::VotingService, type: :service do
  # Test business logic
  # Test error handling
  # Test edge cases
end

# Jobs
RSpec.describe ProcessPaymentJob, type: :job do
  # Test job execution
  # Test retry behavior
  # Test queue assignment
end
```

### Integration Tests (20% of tests)
```ruby
# Controllers/Requests
RSpec.describe 'Circles API', type: :request do
  # Test CRUD operations
  # Test authorization
  # Test error responses
  # Test status codes
end
```

### System Tests (10% of tests)
```ruby
# E2E User Flows
RSpec.describe 'Purchase Flow', type: :system do
  # Test critical user journeys
  # Test JavaScript interactions
  # Test Turbo Frame updates
end
```

### Test Helpers

#### Factory Bot Patterns
```ruby
# spec/factories/circles/circles.rb
FactoryBot.define do
  factory :circle, class: 'Circles::Circle' do
    name { Faker::Company.name }
    monthly_contribution { Money.new(500_00, 'KES') }
    max_members { 20 }
    credit_multiplier { 10 }
    status { :forming }

    trait :active do
      status { :active }
      members_count { 5 }
    end

    trait :with_members do
      transient do
        member_count { 5 }
      end

      after(:create) do |circle, evaluator|
        create_list(:membership, evaluator.member_count, circle: circle)
      end
    end
  end
end

# Usage
circle = create(:circle)  # Basic circle
active_circle = create(:circle, :active)  # Active circle
circle_with_members = create(:circle, :with_members, member_count: 10)
```

#### Custom Matchers
```ruby
# spec/support/matchers/money_matchers.rb
RSpec::Matchers.define :have_money_equal_to do |expected|
  match do |actual|
    actual.fractional == expected.fractional &&
    actual.currency == expected.currency
  end
end

# Usage
expect(circle.monthly_contribution).to have_money_equal_to(Money.new(500_00, 'KES'))
```

---

## Common Patterns

### Money Handling
```ruby
# Always use Money objects
amount = Money.new(5000_00, 'KES')  # KES 5,000

# In models
class Purchase < ApplicationRecord
  monetize :total_amount_cents, with_model_currency: :currency
  monetize :deposit_amount_cents, with_model_currency: :currency
end

# In views
<%= humanized_money_with_symbol(@purchase.total_amount) %>
# Output: KES 5,000.00

# In tests
expect(purchase.total_amount).to eq(Money.new(5000_00, 'KES'))
```

### Event Publishing
```ruby
# After significant state changes
class Circles::Membership < ApplicationRecord
  after_commit :publish_member_joined_event, on: :create

  private

  def publish_member_joined_event
    Circles::Events::MemberJoined.publish(
      circle_id: circle.id,
      user_id: user.id,
      membership_id: id
    )
  end
end

# Event handler
module Circles
  module Subscribers
    class UpdateCreditLimit
      def self.call(event)
        user = User.find(event.user_id)
        Credit::UpdateLimitService.new(user).perform
      end
    end
  end
end
```

### Policy Checks
```ruby
# In controllers
class CirclesController < ApplicationController
  def show
    @circle = Circle.find(params[:id])
    authorize @circle  # Pundit authorization
  end
end

# In policies
class CirclePolicy < ApplicationPolicy
  def show?
    user.present? && (user.admin? || record.members.include?(user))
  end

  def contribute?
    user.present? && record.active? && record.members.include?(user)
  end
end

# In views
<% if policy(@circle).contribute? %>
  <%= link_to "Contribute", new_circle_contribution_path(@circle) %>
<% end %>
```

### Turbo Frame Updates
```ruby
# Controller
def create
  @circle = Circle.new(circle_params)

  if @circle.save
    respond_to do |format|
      format.html { redirect_to @circle }
      format.turbo_stream {
        render turbo_stream: turbo_stream.append(
          "circles",
          partial: "circles/circle",
          locals: { circle: @circle }
        )
      }
    end
  end
end

# View
<%= turbo_frame_tag "circle_#{@circle.id}" do %>
  <%= render partial: "circles/circle", locals: { circle: @circle } %>
<% end %>
```

### Background Job Patterns
```ruby
# Enqueue job
ProcessPaymentJob.perform_later(payment.id)

# Enqueue with delay
SendReminderJob.set(wait: 3.days).perform_later(user.id)

# Enqueue for specific time
GenerateReportJob.set(wait_until: Date.tomorrow.noon).perform_later(report.id)

# Priority queue
SendOtpJob.set(queue: :critical).perform_later(user.id, otp_code)
```

---

## Development Commands

### Daily Workflow
```bash
# Start development server
bin/dev
# This runs:
# - Rails server (port 3000)
# - Solid Queue workers
# - TailwindCSS watcher

# Run tests
bundle exec rspec                      # All tests
bundle exec rspec spec/domains/circles/  # Specific domain
bundle exec rspec --tag ~slow          # Exclude slow tests
COVERAGE=true bundle exec rspec        # With coverage report

# Code quality
bundle exec rubocop                    # Lint all files
bundle exec rubocop -A                 # Auto-fix violations
bundle exec rubocop app/domains/circles/  # Specific directory

# Database
rails db:migrate                       # Run migrations
rails db:rollback                      # Rollback last migration
rails db:reset                         # Drop, create, migrate, seed
rails db:seed                          # Load seed data

# Console
rails console                          # Development console
rails console --sandbox                # Sandbox mode (rollback on exit)

# Routes
rails routes                           # All routes
rails routes | grep circles            # Filter routes

# Solid Queue
rails solid_queue:status               # Check job status
rails solid_queue:clear_all            # Clear all jobs (dev only)
```

### Git Commands
```bash
# Branch management
git checkout dev                       # Switch to dev
git checkout -b feature/new-feature    # Create feature branch
git branch -d feature/old-feature      # Delete local branch
git push origin --delete feature/old   # Delete remote branch

# Commit workflow
git status                             # Check changes
git add .                              # Stage all changes
git add -p                             # Stage interactively
git commit -m "feat: message"          # Commit with message
git commit --amend                     # Amend last commit

# Pull Request
gh pr create --base dev                # Create PR
gh pr list                             # List PRs
gh pr view 123                         # View PR #123
gh pr merge 123                        # Merge PR #123

# Sync with remote
git fetch origin                       # Fetch updates
git pull origin dev                    # Pull dev branch
git rebase origin/dev                  # Rebase on dev
```

### Testing Commands
```bash
# Run specific tests
bundle exec rspec spec/models/         # All model tests
bundle exec rspec spec/services/       # All service tests
bundle exec rspec spec/requests/       # All request tests
bundle exec rspec spec/system/         # All system tests

# Run single file
bundle exec rspec spec/models/circle_spec.rb

# Run single test
bundle exec rspec spec/models/circle_spec.rb:42

# Run with tag
bundle exec rspec --tag focus          # Only tests tagged with :focus
bundle exec rspec --tag ~slow          # Exclude slow tests

# Coverage
COVERAGE=true bundle exec rspec        # Generate coverage report
open coverage/index.html               # View coverage report
```

---

## Troubleshooting

### Database Issues

#### Connection Refused
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Start PostgreSQL
sudo systemctl start postgresql

# Verify credentials
psql -U buystep_commerce -d buystep_commerce_development
```

#### Migration Errors
```bash
# Check migration status
rails db:migrate:status

# Rollback and retry
rails db:rollback
rails db:migrate

# Reset database (⚠️ destroys data)
rails db:drop db:create db:migrate db:seed
```

#### Test Database Issues
```bash
# Prepare test database
rails db:test:prepare

# Reset test database
RAILS_ENV=test rails db:drop db:create db:migrate
```

### Solid Queue Issues

#### Jobs Not Processing
```bash
# Check Solid Queue status
rails solid_queue:status

# View job queue
rails console
SolidQueue::Job.count
SolidQueue::Job.pending.count

# Clear all jobs (dev only)
rails solid_queue:clear_all

# Restart workers
# Stop bin/dev and restart
```

#### Failed Jobs
```bash
# Check failed jobs
rails console
SolidQueue::Job.failed.count

# Retry failed jobs
SolidQueue::Job.failed.find_each(&:retry!)

# View job details
job = SolidQueue::Job.last
job.error
job.backtrace
```

### Test Failures

#### Random Failures
```bash
# Run with seed
bundle exec rspec --seed 12345

# Run with order
bundle exec rspec --order random

# Bisect to find flaky test
bundle exec rspec --bisect
```

#### Factory Errors
```bash
# Validate factories
bundle exec rake factory_bot:lint

# Check factory definitions
rails console
FactoryBot.factories.map(&:name)
```

### Authentication Issues

#### OTP Not Sending
```bash
# Check SMS service configuration
rails console
ENV['SMS_API_KEY']  # Should be present

# Test SMS service
SmsService.send('+254700000001', 'Test message')

# Check logs
tail -f log/development.log | grep SMS
```

#### Session Issues
```bash
# Clear sessions
rails console
ActiveRecord::SessionStore::Session.delete_all

# Check session configuration
# config/initializers/session_store.rb
```

### Performance Issues

#### Slow Tests
```bash
# Profile tests
bundle exec rspec --profile

# Use test optimization
# spec/rails_helper.rb
config.use_transactional_fixtures = true
```

#### Slow Queries
```bash
# Enable query logging
rails console
ActiveRecord::Base.logger = Logger.new(STDOUT)

# Check slow queries in logs
tail -f log/development.log | grep "SELECT"
```

### Code Quality Issues

#### RuboCop Violations
```bash
# Auto-fix safe violations
bundle exec rubocop -A

# Check specific files
bundle exec rubocop app/models/circle.rb

# Disable cop for line
# rubocop:disable Style/MethodName
```

---

## Quick Reference

### Common Test Patterns
```ruby
# Model test
it { should validate_presence_of(:name) }
it { should have_many(:memberships) }

# Service test
expect { service.perform }.to change(Circle, :count).by(1)

# Request test
expect(response).to have_http_status(:ok)
expect(json_response['id']).to be_present

# System test
visit circles_path
click_button 'Create Circle'
expect(page).to have_content('Circle created')
```

### Git Commit Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting
- `refactor`: Code restructure
- `test`: Adding tests
- `chore`: Maintenance

### Keyboard Shortcuts
```bash
# Rails console
Ctrl+D          # Exit
Ctrl+R          # Search history

# Tests
Ctrl+C          # Stop tests
q               # Quit (in --fail-fast mode)
```

---

## Resources

- **Rails Guides**: https://guides.rubyonrails.org/
- **Rails 8 Release Notes**: https://edgeguides.rubyonrails.org/8_0_release_notes.html
- **Solid Queue**: https://github.com/rails/solid_queue
- **Hotwire**: https://hotwired.dev/
- **RSpec**: https://rspec.info/
- **TailwindCSS**: https://tailwindcss.com/

---

**Last Updated**: 2025-01-09
**Maintained By**: Development Team

---

Built with ❤️ using Rails 8 Native Stack
