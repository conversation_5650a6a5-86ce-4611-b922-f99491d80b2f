#!/bin/bash
# ============================================================================
# BUYSTEP COMMERCE PLATFORM - COMPLETE SETUP GUIDE
# Ruby on Rails 8 + Hotwire + TailwindCSS + PostgreSQL
# ============================================================================

# ============================================================================
# PHASE 1: SYSTEM PREREQUISITES
# ============================================================================

echo "=== PHASE 1: Installing System Prerequisites ==="

# Update system packages
sudo apt-get update && sudo apt-get upgrade -y

# Install essential build tools
sudo apt-get install -y build-essential git curl libssl-dev libreadline-dev \
  zlib1g-dev autoconf bison libyaml-dev libreadline-dev libncurses5-dev \
  libffi-dev libgdbm-dev

# Install PostgreSQL 16
sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
sudo apt-get update
sudo apt-get install -y postgresql-16 postgresql-contrib-16 libpq-dev

# Install Redis (for ActionCable, Sidekiq, caching)
sudo apt-get install -y redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Install Node.js 20.x (for JavaScript bundling)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Yarn
npm install -g yarn

# Verify installations
echo "Verifying installations..."
psql --version
redis-cli --version
node --version
yarn --version

# ============================================================================
# PHASE 2: RUBY INSTALLATION
# ============================================================================

echo "=== PHASE 2: Installing Ruby 3.3.0 ==="

# Install rbenv
git clone https://github.com/rbenv/rbenv.git ~/.rbenv
echo 'export PATH="$HOME/.rbenv/bin:$PATH"' >> ~/.bashrc
echo 'eval "$(rbenv init -)"' >> ~/.bashrc
source ~/.bashrc

# Install ruby-build plugin
git clone https://github.com/rbenv/ruby-build.git ~/.rbenv/plugins/ruby-build

# Install Ruby 3.3.0
rbenv install 3.3.0
rbenv global 3.3.0

# Verify Ruby installation
ruby -v
gem -v

# Update RubyGems and install bundler
gem update --system
gem install bundler
rbenv rehash

# ============================================================================
# PHASE 3: DATABASE SETUP
# ============================================================================

echo "=== PHASE 3: Setting up PostgreSQL Database ==="

# Create PostgreSQL user for the application
sudo -u postgres psql -c "CREATE USER buystep_commerce WITH PASSWORD 'your_secure_password' CREATEDB;"
sudo -u postgres psql -c "ALTER USER buystep_commerce WITH SUPERUSER;"

# Create development and test databases
sudo -u postgres psql -c "CREATE DATABASE buystep_commerce_development OWNER buystep_commerce;"
sudo -u postgres psql -c "CREATE DATABASE buystep_commerce_test OWNER buystep_commerce;"

# Grant privileges
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE buystep_commerce_development TO buystep_commerce;"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE buystep_commerce_test TO buystep_commerce;"

# Enable required PostgreSQL extensions
sudo -u postgres psql -d buystep_commerce_development -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;"
sudo -u postgres psql -d buystep_commerce_development -c "CREATE EXTENSION IF NOT EXISTS uuid-ossp;"
sudo -u postgres psql -d buystep_commerce_test -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;"
sudo -u postgres psql -d buystep_commerce_test -c "CREATE EXTENSION IF NOT EXISTS uuid-ossp;"

# ============================================================================
# PHASE 4: RAILS APPLICATION CREATION
# ============================================================================

echo "=== PHASE 4: Creating Rails 8 Application ==="

# Install Rails 8
gem install rails -v '~> 8.0'
rbenv rehash

# Create new Rails application with specific configurations
rails new buystep_commerce \
  --database=postgresql \
  --css=tailwind \
  --javascript=importmap \
  --skip-jbuilder \
  --skip-test \
  -T

cd buystep_commerce

# ============================================================================
# PHASE 5: GEMFILE CONFIGURATION
# ============================================================================

echo "=== PHASE 5: Configuring Gemfile ==="

# Replace Gemfile content
cat > Gemfile << 'EOF'
source "https://rubygems.org"
ruby "3.3.0"

# Rails framework
gem "rails", "~> 8.0.0"

# Database
gem "pg", "~> 1.5"

# Server
gem "puma", ">= 6.0"

# Frontend
gem "turbo-rails"
gem "stimulus-rails"
gem "tailwindcss-rails"
gem "importmap-rails"

# Money handling
gem "money-rails", "~> 1.15"

# Authentication
gem "devise", "~> 4.9"

# Authorization
gem "pundit", "~> 2.3"

# State machines
gem "aasm", "~> 5.5"

# Background jobs
gem "sidekiq", "~> 7.2"

# Notifications
gem "noticed", "~> 2.0"

# Pagination
gem "pagy", "~> 6.2"

# File uploads
gem "image_processing", "~> 1.12"

# HTTP client
gem "http", "~> 5.1"

# Environment variables
gem "dotenv-rails", groups: [:development, :test]

# API documentation
gem "rswag", groups: [:development, :test]

# Monitoring
gem "sentry-ruby"
gem "sentry-rails"

# Performance
gem "rack-timeout"
gem "bootsnap", require: false

# Caching
gem "redis", ">= 4.0.1"

# JSON handling
gem "oj", "~> 3.16"

# Geocoding (for location features)
gem "geocoder", "~> 1.8"

# Country/Currency data
gem "countries", "~> 5.7"

# CSV processing
gem "smarter_csv", "~> 1.10"

group :development, :test do
  gem "debug", platforms: %i[ mri windows ]
  gem "rspec-rails", "~> 6.1"
  gem "factory_bot_rails", "~> 6.4"
  gem "faker", "~> 3.2"
  gem "shoulda-matchers", "~> 6.0"
  gem "database_cleaner-active_record", "~> 2.1"
  gem "simplecov", require: false
  gem "rubocop-rails", require: false
  gem "rubocop-rspec", require: false
end

group :development do
  gem "web-console"
  gem "annotate", "~> 3.2"
  gem "bullet", "~> 7.1"
  gem "letter_opener", "~> 1.9"
  gem "better_errors", "~> 2.10"
  gem "binding_of_caller", "~> 1.0"
  gem "rack-mini-profiler", "~> 3.3"
end

group :test do
  gem "capybara"
  gem "selenium-webdriver"
  gem "webdrivers"
  gem "vcr", "~> 6.2"
  gem "webmock", "~> 3.19"
end
EOF

# Install gems
bundle install

# ============================================================================
# PHASE 6: DATABASE CONFIGURATION
# ============================================================================

echo "=== PHASE 6: Configuring Database ==="

# Update database.yml
cat > config/database.yml << 'EOF'
default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  host: localhost
  username: buystep_commerce
  password: <%= ENV["DATABASE_PASSWORD"] || "your_secure_password" %>

development:
  <<: *default
  database: buystep_commerce_development

test:
  <<: *default
  database: buystep_commerce_test

production:
  <<: *default
  database: buystep_commerce_production
  username: buystep_commerce
  password: <%= ENV["DATABASE_PASSWORD"] %>
EOF

# Create .env file for development
cat > .env << 'EOF'
DATABASE_PASSWORD=your_secure_password
REDIS_URL=redis://localhost:6379/0

# Application
APP_URL=http://localhost:3000
SECRET_KEY_BASE=

# M-Pesa (Kenya Mobile Money)
MPESA_CONSUMER_KEY=
MPESA_CONSUMER_SECRET=
MPESA_SHORTCODE=
MPESA_PASSKEY=
MPESA_ENV=sandbox

# WhatsApp Business API
WHATSAPP_PHONE_NUMBER_ID=
WHATSAPP_ACCESS_TOKEN=
WHATSAPP_APP_SECRET=
WHATSAPP_VERIFY_TOKEN=

# SMTP Settings
SMTP_ADDRESS=smtp.gmail.com
SMTP_PORT=587
SMTP_DOMAIN=localhost
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_AUTHENTICATION=plain
SMTP_ENABLE_STARTTLS_AUTO=true

# Monitoring (Optional)
SENTRY_DSN=
EOF

# Generate secret key
echo "SECRET_KEY_BASE=$(rails secret)" >> .env

# Add .env to .gitignore
echo ".env" >> .gitignore
echo ".env.local" >> .gitignore

# ============================================================================
# PHASE 7: INITIALIZE DATABASE
# ============================================================================

echo "=== PHASE 7: Creating Database ==="

# Create databases
rails db:create

# ============================================================================
# PHASE 8: INSTALL TESTING FRAMEWORK
# ============================================================================

echo "=== PHASE 8: Setting up RSpec ==="

# Install RSpec
rails generate rspec:install

# Configure RSpec
cat > spec/rails_helper.rb << 'EOF'
require 'spec_helper'
ENV['RAILS_ENV'] ||= 'test'
require_relative '../config/environment'

abort("The Rails environment is running in production mode!") if Rails.env.production?
require 'rspec/rails'
require 'shoulda/matchers'

begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  abort e.to_s.strip
end

RSpec.configure do |config|
  config.fixture_path = "#{Rails.root}/spec/fixtures"
  config.use_transactional_fixtures = true
  config.infer_spec_type_from_file_location!
  config.filter_rails_from_backtrace!

  # Factory Bot
  config.include FactoryBot::Syntax::Methods

  # Database Cleaner
  config.before(:suite) do
    DatabaseCleaner.strategy = :transaction
    DatabaseCleaner.clean_with(:truncation)
  end

  config.around(:each) do |example|
    DatabaseCleaner.cleaning do
      example.run
    end
  end
end

# Shoulda Matchers
Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end
EOF

# ============================================================================
# PHASE 9: DEVISE SETUP (Authentication)
# ============================================================================

echo "=== PHASE 9: Installing Devise ==="

# Install Devise
rails generate devise:install

# Configure Devise
cat >> config/environments/development.rb << 'EOF'

  # Devise configuration
  config.action_mailer.default_url_options = { host: 'localhost', port: 3000 }
EOF

# Generate User model (we'll customize this later)
rails generate devise User

# ============================================================================
# PHASE 10: PUNDIT SETUP (Authorization)
# ============================================================================

echo "=== PHASE 10: Installing Pundit ==="

# Install Pundit
rails generate pundit:install

# ============================================================================
# PHASE 11: MONEY-RAILS CONFIGURATION
# ============================================================================

echo "=== PHASE 11: Configuring Money-Rails ==="

# Generate money-rails initializer
rails generate money_rails:initializer

# Update money initializer
cat > config/initializers/money.rb << 'EOF'
MoneyRails.configure do |config|
  config.default_currency = :KES  # Kenyan Shilling as base

  # Allow multiple currencies
  config.include_validations = true
  config.no_cents_if_whole = false
  config.default_format = {
    no_cents_if_whole: false,
    symbol: true
  }

  # Handle rounding
  config.rounding_mode = BigDecimal::ROUND_HALF_UP
end
EOF

# ============================================================================
# PHASE 12: SIDEKIQ CONFIGURATION
# ============================================================================

echo "=== PHASE 12: Configuring Sidekiq ==="

# Create Sidekiq configuration
cat > config/sidekiq.yml << 'EOF'
:concurrency: 5
:max_retries: 3

:queues:
  - critical
  - default
  - mailers
  - low_priority

development:
  :concurrency: 2

production:
  :concurrency: 10
EOF

# Add Sidekiq initializer
cat > config/initializers/sidekiq.rb << 'EOF'
Sidekiq.configure_server do |config|
  config.redis = { url: ENV.fetch("REDIS_URL") { "redis://localhost:6379/0" } }
end

Sidekiq.configure_client do |config|
  config.redis = { url: ENV.fetch("REDIS_URL") { "redis://localhost:6379/0" } }
end
EOF

# Update cable.yml to use Redis
cat > config/cable.yml << 'EOF'
development:
  adapter: redis
  url: <%= ENV.fetch("REDIS_URL") { "redis://localhost:6379/1" } %>
  channel_prefix: buystep_commerce_development

test:
  adapter: test

production:
  adapter: redis
  url: <%= ENV.fetch("REDIS_URL") { "redis://localhost:6379/1" } %>
  channel_prefix: buystep_commerce_production
EOF

# Mount Sidekiq web UI in routes
cat > config/routes.rb << 'EOF'
require 'sidekiq/web'

Rails.application.routes.draw do
  # Sidekiq Web UI (protect this in production!)
  mount Sidekiq::Web => '/sidekiq' if Rails.env.development?

  # Devise routes
  devise_for :users

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  root "home#index"
end
EOF

# ============================================================================
# PHASE 13: TAILWIND CSS CONFIGURATION
# ============================================================================

echo "=== PHASE 13: Configuring TailwindCSS ==="

# TailwindCSS should already be configured, update config
cat > config/tailwind.config.js << 'EOF'
const defaultTheme = require('tailwindcss/defaultTheme')

module.exports = {
  content: [
    './public/*.html',
    './app/helpers/**/*.rb',
    './app/javascript/**/*.js',
    './app/views/**/*.{erb,haml,html,slim}',
    './app/components/**/*.{erb,haml,html,slim,rb}'
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter var', ...defaultTheme.fontFamily.sans],
      },
      colors: {
        'buystep-orange': '#E95420',
        'buystep-purple': '#772953',
        'buystep-blue': '#335280',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ]
}
EOF

# Install Tailwind plugins
yarn add @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio

# ============================================================================
# PHASE 14: APPLICATION CONTROLLER SETUP
# ============================================================================

echo "=== PHASE 14: Configuring Application Controller ==="

cat > app/controllers/application_controller.rb << 'EOF'
class ApplicationController < ActionController::Base
  include Pundit::Authorization

  # Only allow modern browsers
  allow_browser versions: :modern

  before_action :configure_permitted_parameters, if: :devise_controller?

  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized

  private

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [:phone_number, :country_code])
    devise_parameter_sanitizer.permit(:account_update, keys: [:phone_number, :country_code])
  end

  def user_not_authorized(exception)
    policy_name = exception.policy.class.to_s.underscore
    flash[:alert] = t("#{policy_name}.#{exception.query}",
                      scope: 'pundit',
                      default: :default)

    respond_to do |format|
      format.html { redirect_back(fallback_location: root_path) }
      format.turbo_stream {
        render turbo_stream: turbo_stream.replace(
          'flash',
          partial: 'shared/flash',
          locals: { flash: flash }
        ), status: :forbidden
      }
      format.json { render json: { error: flash[:alert] }, status: :forbidden }
    end
  end
end
EOF

# ============================================================================
# PHASE 15: CREATE DIRECTORY STRUCTURE
# ============================================================================

echo "=== PHASE 15: Creating Domain-Driven Directory Structure ==="

# Create domain directories
mkdir -p app/domains/circles/{models,services,jobs,subscribers,events}
mkdir -p app/domains/transactions/{models,services,jobs}
mkdir -p app/domains/credit/{models,services,jobs,subscribers,events}
mkdir -p app/domains/inventory/{models,services,jobs}
mkdir -p app/domains/merchants/{models,services,jobs}
mkdir -p app/domains/diaspora/{models,services,jobs,events}
mkdir -p app/domains/group_buying/{models,services,jobs}
mkdir -p app/domains/identity/{models,services}
mkdir -p app/domains/shared/{events,money,policies}

# Create policy directories
mkdir -p app/policies/{circles,transactions,inventory,diaspora,group_buying}

# Create service directories
mkdir -p app/services/{ussd,whatsapp,mobile_payment}

# Create components directory (ViewComponents)
mkdir -p app/components

# Create channels directory for ActionCable
mkdir -p app/channels

# ============================================================================
# PHASE 16: CREATE BASE MIGRATIONS
# ============================================================================

echo "=== PHASE 16: Generating Base Migrations ==="

# Update User migration for our custom fields
cat > db/migrate/$(date +%Y%m%d%H%M%S)_devise_create_users.rb << 'EOF'
class DeviseCreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users do |t|
      # STI for different user types
      t.string :role, null: false, index: true
      
      # Basic info
      t.string :phone_number, null: false, index: { unique: true }
      t.string :email, index: { unique: true }
      t.string :country_code, null: false, limit: 2
      t.string :currency, null: false, limit: 3, default: 'KES'
      
      # Status
      t.integer :status, default: 0, null: false
      t.integer :verification_level, default: 0, null: false
      
      # Devise
      t.string :encrypted_password, null: false
      t.string :reset_password_token, index: { unique: true }
      t.datetime :reset_password_sent_at
      t.datetime :remember_created_at
      t.string :confirmation_token, index: { unique: true }
      t.datetime :confirmed_at
      t.datetime :confirmation_sent_at
      t.string :unconfirmed_email
      t.integer :failed_attempts, default: 0, null: false
      t.string :unlock_token, index: { unique: true }
      t.datetime :locked_at
      
      # Trackable
      t.datetime :last_sign_in_at
      t.inet :last_sign_in_ip
      
      # Merchant-specific fields (for STI)
      t.string :business_name, index: { unique: true }
      t.integer :business_type
      t.integer :verification_status, default: 0
      t.decimal :fee_percentage, precision: 5, scale: 2, default: 2.0
      t.datetime :last_sale_at, index: true

      t.timestamps
      t.datetime :deleted_at, index: true
    end
  end
end
EOF

# Generate StoredEvents table for event sourcing
rails generate migration CreateStoredEvents \
  event_type:string \
  event_id:string \
  aggregate_id:string \
  payload:jsonb \
  occurred_at:datetime

# Generate USSD sessions table
rails generate migration CreateUssdSessions \
  session_id:string \
  phone_number:string \
  user:references \
  state:string \
  data:text

# ============================================================================
# PHASE 17: CREATE INITIAL CONTROLLERS
# ============================================================================

echo "=== PHASE 17: Creating Initial Controllers ==="

# Generate home controller
rails generate controller Home index --skip-routes

cat > app/controllers/home_controller.rb << 'EOF'
class HomeController < ApplicationController
  skip_before_action :authenticate_user!, only: [:index]

  def index
    if user_signed_in?
      redirect_to dashboard_path
    else
      render :index
    end
  end
end
EOF

# Create basic home view
cat > app/views/home/<USER>'EOF'
<div class="min-h-screen bg-gradient-to-br from-buystep-orange to-buystep-purple">
  <div class="container mx-auto px-4 py-16">
    <div class="text-center text-white">
      <h1 class="text-6xl font-bold mb-4">Buystep Commerce</h1>
      <p class="text-2xl mb-8">Community-Powered Commerce for Africa</p>
      
      <div class="flex justify-center space-x-4">
        <%= link_to "Sign Up", new_user_registration_path, 
            class: "bg-white text-buystep-orange px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition" %>
        <%= link_to "Sign In", new_user_session_path, 
            class: "bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-buystep-orange transition" %>
      </div>
    </div>
  </div>
</div>
EOF

# ============================================================================
# PHASE 18: SETUP MAILER CONFIGURATION
# ============================================================================

echo "=== PHASE 18: Configuring Mailers ==="

# Update development mailer settings
cat >> config/environments/development.rb << 'EOF'

  # Letter Opener for email preview
  config.action_mailer.delivery_method = :letter_opener
  config.action_mailer.perform_deliveries = true
EOF

# Update production mailer settings
cat >> config/environments/production.rb << 'EOF'

  # SMTP settings for production
  config.action_mailer.delivery_method = :smtp
  config.action_mailer.perform_deliveries = true
  config.action_mailer.raise_delivery_errors = true
  config.action_mailer.default_url_options = { host: ENV['APP_URL'] }
  
  config.action_mailer.smtp_settings = {
    address: ENV['SMTP_ADDRESS'],
    port: ENV['SMTP_PORT'],
    domain: ENV['SMTP_DOMAIN'],
    user_name: ENV['SMTP_USERNAME'],
    password: ENV['SMTP_PASSWORD'],
    authentication: ENV['SMTP_AUTHENTICATION'],
    enable_starttls_auto: ENV['SMTP_ENABLE_STARTTLS_AUTO']
  }
EOF

# ============================================================================
# PHASE 19: SETUP ASSET PIPELINE
# ============================================================================

echo "=== PHASE 19: Configuring Assets ==="

# Update application.css
cat > app/assets/stylesheets/application.tailwind.css << 'EOF'
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom components */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-semibold transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-buystep-orange text-white hover:bg-orange-600;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .input-field {
    @apply border border-gray-300 rounded-lg px-4 py-2 w-full focus:ring-2 focus:ring-buystep-orange focus:border-transparent;
  }
}
EOF

# ============================================================================
# PHASE 20: CREATE SHARED LAYOUTS
# ============================================================================

echo "=== PHASE 20: Creating Shared Layout Components ==="

# Create flash partial
mkdir -p app/views/shared
cat > app/views/shared/_flash.html.erb << 'EOF'
<div id="flash" class="fixed top-4 right-4 z-50 space-y-2">
  <% flash.each do |type, message| %>
    <div data-controller="flash" 
         data-flash-duration-value="5000"
         class="<%= type == 'notice' ? 'bg-green-500' : 'bg-red-500' %> text-white px-6 py-3 rounded-lg shadow-lg">
      <%= message %>
    </div>
  <% end %>
</div>
EOF

# Create flash Stimulus controller
mkdir -p app/javascript/controllers
cat > app/javascript/controllers/flash_controller.js << 'EOF'
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { duration: { type: Number, default: 5000 } }

  connect() {
    setTimeout(() => {
      this.dismiss()
    }, this.durationValue)
  }

  dismiss() {
    this.element.remove()
  }
}
EOF

# Update application layout
cat > app/views/layouts/application.html.erb << 'EOF'
<!DOCTYPE html>
<html>
  <head>
    <title>Buystep Commerce</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-50">
    <%= render "shared/flash" %>
    <%= yield %>
  </body>
</html>
EOF

# ============================================================================
# PHASE 21: SETUP ANNOTATE GEM
# ============================================================================

echo "=== PHASE 21: Configuring Annotate ==="

# Generate annotate configuration
rails generate annotate:install

# ============================================================================
# PHASE 22: SETUP BULLET (N+1 Query Detection)
# ============================================================================

echo "=== PHASE 22: Configuring Bullet ==="

cat >> config/environments/development.rb << 'EOF'

  # Bullet configuration
  config.after_initialize do
    Bullet.enable = true
    Bullet.alert = true
    Bullet.bullet_logger = true
    Bullet.console = true
    Bullet.rails_logger = true
    Bullet.add_footer = true
  end
EOF

# ============================================================================
# PHASE 23: SETUP STIMULUS CONTROLLERS
# ============================================================================

echo "=== PHASE 23: Setting up Stimulus ==="

# Update Stimulus application.js
cat > app/javascript/application.js << 'EOF'
import "@hotwired/turbo-rails"
import "./controllers"
EOF

# ============================================================================
# PHASE 24: CREATE PROCFILE FOR DEVELOPMENT
# ============================================================================

echo "=== PHASE 24: Creating Procfile.dev ==="

cat > Procfile.dev << 'EOF'
web: bin/rails server -p 3000
css: bin/rails tailwindcss:watch
sidekiq: bundle exec sidekiq -C config/sidekiq.yml
EOF

# Install Foreman for process management
gem install foreman

# ============================================================================
# PHASE 25: SETUP RUBOCOP (Code Quality)
# ============================================================================

echo "=== PHASE 25: Configuring RuboCop ==="

cat > .rubocop.yml << 'EOF'
require:
  - rubocop-rails
  - rubocop-rspec

AllCops:
  NewCops: enable
  TargetRubyVersion: 3.3
  Exclude:
    - 'db/schema.rb'
    - 'db/migrate/*'
    - 'bin/*'
    - 'vendor/**/*'
    - 'node_modules/**/*'

Style/Documentation:
  Enabled: false

Metrics/BlockLength:
  Exclude:
    - 'spec/**/*'
    - 'config/**/*'

Layout/LineLength:
  Max: 120
  Exclude:
    - 'config/**/*'
EOF

# ============================================================================
# PHASE 26: INITIALIZE GIT REPOSITORY
# ============================================================================

echo "=== PHASE 26: Initializing Git Repository ==="

# Initialize git
git init
git add .
git commit -m "Initial commit: Buystep Commerce Platform setup"

# ============================================================================
# PHASE 27: RUN DATABASE MIGRATIONS
# ============================================================================

echo "=== PHASE 27: Running Database Migrations ==="

# Run migrations
rails db:migrate
rails db:migrate RAILS_ENV=test

# ============================================================================
# PHASE 28: CREATE SEED DATA (OPTIONAL)
# ============================================================================

echo "=== PHASE 28: Creating Seed Data ==="

cat > db/seeds.rb << 'EOF'
# This file should contain all the record creation needed to seed the database

puts "🌱 Seeding database..."

# Create admin user
admin = User.create!(
  email: '<EMAIL>',
  phone_number: '+254700000000',
  country_code: 'KE',
  currency: 'KES',
  password: 'password123',
  password_confirmation: 'password123',
  role: 'Admin',
  confirmed_at: Time.current
)

puts "✅ Created admin user: #{admin.email}"

# Create test consumer
consumer = User.create!(
  email: '<EMAIL>',
  phone_number: '+254700000001',
  country_code: 'KE',
  currency: 'KES',
  password: 'password123',
  password_confirmation: 'password123',
  role: 'Consumer',
  confirmed_at: Time.current
)

consumer.create_profile!(
  first_name: 'John',
  last_name: 'Doe',
  date_of_birth: 25.years.ago
)

puts "✅ Created test consumer: #{consumer.email}"

puts "🎉 Seeding complete!"
EOF

# Run seeds
rails db:seed

# ============================================================================
# PHASE 29: VERIFY INSTALLATION
# ============================================================================

echo "=== PHASE 29: Verifying Installation ==="

# Check if Rails console works
rails runner "puts 'Rails environment loaded successfully!'"

# Check if Redis is running
redis-cli ping

# Check if PostgreSQL is running
sudo systemctl status postgresql

# ============================================================================
# PHASE 30: FINAL STEPS & DOCUMENTATION
# ============================================================================

echo "=== PHASE 30: Creating Documentation ==="

cat > README.md << 'EOF'
# Buystep Commerce Platform

Community-powered commerce platform for Africa built with Ruby on Rails 8.

## Prerequisites

- Ruby 3.3.0
- Rails 8.0+
- PostgreSQL 16+
- Redis 7+
- Node.js 20+

## Setup

```bash
# Install dependencies
bundle install
yarn install

# Setup database
rails db:create db:migrate db:seed

# Start development server
foreman start -f Procfile.dev
```

## Development

- Main app: http://localhost:3000
- Sidekiq UI: http://localhost:3000/sidekiq

## Testing

```bash
# Run all tests
bundle exec rspec

# Run with coverage
COVERAGE=true bundle exec rspec
```

## Architecture

This application follows Domain-Driven Design (DDD) principles:

- `app/domains/` - Bounded contexts (Circles, Transactions, Credit, etc.)
- `app/policies/` - Authorization policies (Pundit)
- `app/services/` - Business logic and integrations
- `app/components/` - Reusable view components

## Key Features

- 🔐 Secure authentication with Devise
- 💰 Multi-currency support with Money-Rails
- 🔄 Real-time updates with Hotwire/Turbo
- 📱 Mobile-first design with TailwindCSS
- 🌍 African mobile money integration (M-Pesa)
- 📲 USSD & WhatsApp support
- 🎯 Background job processing with Sidekiq
- 📊 Comprehensive test coverage with RSpec

## Deployment

See `docs/DEPLOYMENT.md` for production deployment instructions.

## License

Proprietary - All rights reserved
EOF

# ============================================================================
# COMPLETION MESSAGE
# ============================================================================

cat << 'EOF'

╔════════════════════════════════════════════════════════════════════════╗
║                                                                        ║
║     ✨ BUYSTEP COMMERCE PLATFORM SETUP COMPLETE! ✨                    ║
║                                                                        ║
║     Your Rails 8 application is ready for development!                ║
║                                                                        ║
╚════════════════════════════════════════════════════════════════════════╝

📁 Project Structure Created:
   ├── app/domains/          (Domain-Driven Design structure)
   ├── app/policies/         (Authorization policies)
   ├── app/services/         (Business logic)
   └── spec/                 (RSpec tests)

🔧 Next Steps:

   1. Start the development server:
      $ foreman start -f Procfile.dev

   2. Visit the application:
      🌐 http://localhost:3000

   3. Access Sidekiq dashboard:
      📊 http://localhost:3000/sidekiq

   4. Run tests:
      $ bundle exec rspec

   5. Start building your domain models:
      $ rails generate model Circles::Circle name:string

📚 Important Files:
   - .env                    (Environment variables)
   - config/database.yml     (Database configuration)
   - Procfile.dev           (Development processes)
   - config/routes.rb       (Application routes)

🎯 Development Credentials:
   - Admin: <EMAIL> / password123
   - Consumer: <EMAIL> / password123

📖 Documentation:
   - README.md              (Project overview)
   - Architecture guide in app/domains/

Happy coding! 🚀

EOF
