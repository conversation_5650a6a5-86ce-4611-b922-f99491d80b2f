# Buystep Commerce Platform

A community-powered BNPL (Buy Now, Pay Later) platform designed specifically for African markets. Buystep leverages community trust networks (Circles) to provide credit access to underbanked populations while minimizing investor capital requirements.

[![CI](https://github.com/yourusername/buystep/workflows/CI/badge.svg)](https://github.com/yourusername/buystep/actions)
[![Ruby Version](https://img.shields.io/badge/ruby-3.3+-red.svg)](https://www.ruby-lang.org)
[![Rails Version](https://img.shields.io/badge/rails-8.1+-red.svg)](https://rubyonrails.org)

## 🌍 Mission

Enabling financial inclusion across Africa by combining community trust networks with modern technology, providing access to credit and purchasing power for the underbanked.

## ✨ Key Features

- **🤝 Community Guarantee Circles**: Self-organized groups providing mutual credit guarantees
- **💳 BNPL for Essential Goods**: Buy now, pay later for groceries, medications, and essentials
- **📊 Dynamic Credit Scoring**: Circle participation and payment history based scoring
- **💰 Multi-Currency Support**: KES, NGN, GHS, TZS, UGX with automatic conversion
- **📱 Mobile-First Design**: Works seamlessly on feature phones (USSD *384#)
- **🔐 Passwordless Auth**: SMS-based OTP authentication for accessibility
- **💸 Mobile Money Integration**: M-Pesa, MTN Mobile Money, Airtel Money
- **🌐 Diaspora Guarantees**: Overseas relatives can provide credit guarantees

## 🛠 Technology Stack

| Component | Technology | Purpose |
|-----------|-----------|---------|
| **Backend** | Ruby on Rails 8.1+ | Application framework |
| **Frontend** | Hotwire (Turbo + Stimulus) | Progressive enhancement |
| **Styling** | TailwindCSS 3.0+ | Mobile-first responsive design |
| **Database** | PostgreSQL 16+ | Primary data store |
| **Cache** | Solid Cache | Database-backed caching (no Redis) |
| **Jobs** | Solid Queue | Background processing (no Sidekiq) |
| **WebSockets** | Solid Cable | Real-time features (no Redis) |
| **Authentication** | Devise Passwordless | OTP-based authentication |
| **Authorization** | Pundit | Policy-based authorization |
| **Money** | Money-Rails | Multi-currency handling |
| **Testing** | RSpec + Capybara | Test framework |
| **Deployment** | Kamal 2 | Docker-based deployment |

### Why Rails 8 Native Stack?

**Single Database Architecture**: Uses PostgreSQL for everything (application data, cache, job queue, WebSocket messages) instead of Redis/Sidekiq:
- ✅ Simpler infrastructure
- ✅ Single backup strategy
- ✅ Lower operational costs
- ✅ Better for resource-constrained environments
- ✅ Built-in job history and recurring tasks

## 🚀 Quick Start

### Prerequisites

- Ruby 3.3 or higher
- PostgreSQL 16 or higher
- Node.js 20 or higher (for asset compilation)

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/buystep.git
cd buystep

# Install dependencies
bin/setup

# Create and setup database
bin/rails db:create db:migrate db:seed

# Start development server
bin/dev
```

Visit `http://localhost:3000` to see the application.

## 📋 Development Workflow

### Test-Driven Development (TDD)

**Required**: Write tests before implementation following the Red-Green-Refactor cycle.

```bash
# Run all tests
bin/rails test

# Run specific test file
bin/rails test test/models/circle_test.rb

# Run test at specific line
bin/rails test test/models/circle_test.rb:10

# Run system tests
bin/rails test:system
```

**Test Coverage Requirement**: ≥90%

### Git Workflow

**Branch Protection**: No direct commits to `main` or `dev`.

```bash
# Create feature branch
git checkout -b feature/descriptive-name

# Make changes with TDD
# 1. Write failing test (RED)
# 2. Make test pass (GREEN)
# 3. Refactor code

# Commit with semantic format
git commit -m "feat(circles): add voting system for proposals"

# Push and create PR
git push origin feature/descriptive-name
gh pr create --base dev
```

**Semantic Commit Format**:
```
type(scope): subject

feat(circles): add voting functionality
fix(credit): correct score calculation
docs(readme): update setup instructions
test(transactions): add payment flow tests
```

### Code Quality Checks

```bash
# Lint Ruby code
bin/rubocop
bin/rubocop -a  # Auto-fix issues

# Security scans
bin/brakeman          # Rails security scan
bin/bundler-audit     # Check gem vulnerabilities
bin/importmap audit   # Check JS dependencies
```

All checks must pass before PR merge.

## 🏗 Architecture

### Domain-Driven Design

The application uses DDD with bounded contexts in `app/domains/`:

```
app/domains/
├── circles/          # Community Guarantee Circles
│   ├── models/
│   ├── services/
│   ├── jobs/
│   └── events/
├── transactions/     # Payment processing
├── credit/           # Credit scoring & underwriting
├── inventory/        # Merchant inventory financing
├── merchants/        # Merchant management
├── diaspora/         # Diaspora guarantee programs
├── group_buying/     # Collective purchasing
├── identity/         # Authentication & KYC
└── shared/           # Cross-domain concerns
```

### Key Design Patterns

**Service Objects**: Encapsulate business logic
```ruby
module Circles
  class CreateService
    def call
      ActiveRecord::Base.transaction do
        # Business logic here
      end
    end
  end
end
```

**Background Jobs**: Solid Queue for async processing
```ruby
class NotificationJob < ApplicationJob
  queue_as :default
  recurring schedule: "0 9 * * *"  # Daily at 9 AM

  def perform(user_id)
    # Job logic
  end
end
```

**Money Handling**: Always use Money-Rails
```ruby
payment = Payment.new(amount: Money.new(10000, 'KES'))
payment.amount.format  # "KSh 100.00"
```

## 🧪 Testing

### Test Structure (70/20/10 Pyramid)

- **70% Unit Tests**: Models, services, jobs
- **20% Integration Tests**: Controllers, APIs
- **10% System Tests**: Full user flows

### Example Test

```ruby
RSpec.describe Circles::VotingService do
  describe '#cast_vote' do
    subject(:service) { described_class.new(proposal, voter) }

    let(:proposal) { create(:proposal) }
    let(:voter) { create(:circle_member) }

    it 'creates a vote record' do
      expect { service.call }.to change(Vote, :count).by(1)
    end
  end
end
```

## 🌍 African Market Features

### Mobile Money Integration
- **M-Pesa** (Kenya, Tanzania)
- **MTN Mobile Money** (Ghana, Uganda, Nigeria)
- **Airtel Money** (Multiple countries)

### Multi-Currency Support
- KES (Kenyan Shilling)
- NGN (Nigerian Naira)
- GHS (Ghanaian Cedi)
- TZS (Tanzanian Shilling)
- UGX (Ugandan Shilling)

### Feature Phone Support
- **USSD Interface**: Dial `*384#` for access
- **SMS Commands**: Basic operations via SMS
- **WhatsApp Business**: Rich interactions via WhatsApp

### Passwordless Authentication
- OTP sent via SMS
- 6-digit verification codes
- 20-minute expiry
- No password storage or management

## 🚢 Deployment

### Kamal 2 Deployment

```bash
# Setup deployment
kamal setup

# Deploy updates
kamal deploy

# Check status
kamal app logs
```

### Database Migrations

```bash
# Production migrations
kamal app exec -i "bin/rails db:migrate"

# Rollback
kamal app exec -i "bin/rails db:rollback"
```

## 📚 Documentation

- **[DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)**: Comprehensive development guide with TDD workflow, Git practices, and Rails 8 configuration
- **[CLAUDE.md](CLAUDE.md)**: Instructions for AI coding assistants
- **[claude-project-docs.md](claude-project-docs.md)**: Complete PRD, user personas, and feature specifications

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/your-feature`
3. **Write tests first** (TDD is required)
4. **Implement feature** with tests passing
5. **Run linters**: `bin/rubocop -a`
6. **Run security scans**: `bin/brakeman && bin/bundler-audit`
7. **Ensure ≥90% coverage**
8. **Commit**: Use semantic commit format
9. **Push branch**: `git push origin feature/your-feature`
10. **Create Pull Request** to `dev` branch

### PR Requirements
- ✅ All tests passing
- ✅ RuboCop compliance
- ✅ Security scans pass
- ✅ Test coverage ≥90%
- ✅ 1+ code review approvals

## 🔒 Security

- **Passwordless Authentication**: No password storage
- **Input Validation**: All user input validated and sanitized
- **Authorization**: Pundit policies on every action
- **Security Scans**: Automated Brakeman and dependency audits
- **Encryption**: Sensitive data encrypted at rest
- **Regular Updates**: Automated dependency updates via Dependabot

## 📊 Performance

- **Core Web Vitals**: Optimized for mobile networks
- **Database Caching**: Solid Cache for 10TB+ capacity
- **Background Jobs**: Async processing for heavy operations
- **Asset Optimization**: Propshaft for modern asset pipeline
- **Mobile-First**: Designed for limited bandwidth

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/yourusername/buystep/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/buystep/discussions)
- **Email**: <EMAIL>

## 📜 License

This project is proprietary software. All rights reserved.

## 👥 Team

Built with ❤️ for Africa by the Buystep team.

---

**Note**: This is a Rails 8 application using the native Solid stack (Solid Cache, Solid Queue, Solid Cable). Do not use Redis or Sidekiq.
