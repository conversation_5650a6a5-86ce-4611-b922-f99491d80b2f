# frozen_string_literal: true

# FactoryBot configuration for RSpec
# See: https://github.com/thoughtbot/factory_bot/blob/master/GETTING_STARTED.md

RSpec.configure do |config|
  # Include FactoryBot syntax methods (build, create, etc.) in specs
  config.include FactoryBot::Syntax::Methods

  # Lint factories after loading to catch errors early
  config.before(:suite) do
    FactoryBot.lint
  end
end
