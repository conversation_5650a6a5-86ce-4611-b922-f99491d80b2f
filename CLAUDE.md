# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development Setup
```bash
bin/setup                    # Initial project setup
bin/rails db:create db:migrate  # Database setup
bin/dev                      # Start development server (Puma + Tailwind watch)
```

### Testing
```bash
bin/rails test               # Run all tests
bin/rails test:system        # Run system tests only
bin/rails test test/models/user_test.rb  # Run single test file
bin/rails test test/models/user_test.rb:10  # Run test at specific line
```

### Code Quality
```bash
bin/rubocop                  # Lint code
bin/rubocop -a               # Auto-fix linting issues
bin/brakeman                 # Security vulnerability scan
bin/bundler-audit            # Check for vulnerable gems
bin/importmap audit          # Check JavaScript dependencies
```

### Database Operations
```bash
bin/rails db:migrate         # Run pending migrations
bin/rails db:rollback        # Rollback last migration
bin/rails db:reset           # Drop, create, migrate, seed
bin/rails db:seed            # Load seed data
```

## Architecture Overview

### Rails 8 Native Stack (No Redis/Sidekiq)

**Critical**: This project uses Rails 8's native database-backed solutions:
- **Solid Cache**: Database-backed caching (replaces Redis for caching)
- **Solid Queue**: Database-backed background jobs (replaces Sidekiq)
- **Solid Cable**: Database-backed WebSockets (replaces ActionCable with Redis)

**Multi-database configuration** (see `config/database.yml`):
```
production:
  primary:   buystep_production         # Main application data
  cache:     buystep_production_cache   # Solid Cache data
  queue:     buystep_production_queue   # Solid Queue jobs
  cable:     buystep_production_cable   # Solid Cable messages
```

### Domain-Driven Design Structure

The application follows DDD with bounded contexts in `app/domains/`:
- `circles/` - Community Guarantee Circles (core business logic)
- `transactions/` - Payment processing & purchases
- `credit/` - Credit scoring & underwriting
- `inventory/` - Merchant inventory financing
- `merchants/` - Merchant management
- `diaspora/` - Diaspora guarantee programs
- `group_buying/` - Collective purchasing power
- `identity/` - User authentication & KYC
- `shared/` - Cross-domain concerns (events, money, policies)

Each domain contains:
- `models/` - ActiveRecord models
- `services/` - Business logic (single responsibility classes)
- `jobs/` - Background processing (Solid Queue)
- `events/` - Domain events for async communication
- `subscribers/` - Event handlers

### Authentication: Passwordless (OTP-based)

Uses Devise passwordless authentication with OTP (One-Time Password):
- 6-digit codes sent via SMS
- 20-minute expiry
- No password storage
- Critical for African market (SMS-based, works with feature phones)

## Non-Negotiable Development Practices

### Test-Driven Development (TDD)
- **Always write tests BEFORE implementation**
- Follow Red-Green-Refactor cycle:
  1. **Red**: Write failing test
  2. **Green**: Minimal code to pass
  3. **Refactor**: Clean up while tests pass
- Required coverage: ≥90%
- Test structure: Arrange-Act-Assert

### Git Workflow
**Branch Protection**: No direct commits to `main` or `dev`

Standard workflow:
```bash
git checkout -b feature/descriptive-name
# Write tests → Implement → All tests pass
git commit -m "feat(domain): description"
git push origin feature/descriptive-name
gh pr create --base dev
```

**Semantic Commit Format**: `type(scope): subject`
- Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`
- Scope: Domain name (e.g., `circles`, `credit`, `identity`)
- Example: `feat(circles): add voting system for proposals`

**PR Requirements** (enforced by CI):
- All tests passing (unit + system)
- RuboCop compliance (bin/rubocop)
- Security scans pass (Brakeman, bundler-audit)
- Test coverage ≥90%
- 1+ approvals

## Critical Patterns

### Service Objects
Single responsibility business logic:
```ruby
module Circles
  class CreateService
    def initialize(user, params)
      @user = user
      @params = params
    end

    def call
      ActiveRecord::Base.transaction do
        circle = create_circle
        create_initial_membership(circle)
        publish_circle_created_event(circle)
        circle
      end
    rescue ActiveRecord::RecordInvalid => e
      { success: false, errors: e.record.errors }
    end

    private
    # Implementation details
  end
end
```

### Background Jobs (Solid Queue)
```ruby
class ApplicationJob < ActiveJob::Base
  retry_on StandardError, wait: :exponentially_longer, attempts: 5
  discard_on ActiveRecord::RecordNotFound
  queue_as :default

  # Solid Queue specific - built-in cron support
  recurring schedule: "0 9 * * *"  # Daily at 9 AM
end
```

### Money Handling
Always use Money-Rails for currency:
```ruby
# In models
monetize :amount_cents, with_model_currency: :currency

# In code
payment = Payment.new(amount: Money.new(10000, 'KES'))  # 100.00 KES
payment.amount.format  # "KSh 100.00"
```

### Authorization (Pundit)
Every controller action must authorize:
```ruby
class CirclesController < ApplicationController
  before_action :authenticate_user!
  after_action :verify_authorized

  def show
    @circle = Circle.find(params[:id])
    authorize @circle
  end
end
```

### Turbo Frames (Hotwire)
Progressive enhancement for dynamic updates:
```erb
<%= turbo_frame_tag dom_id(@circle) do %>
  <div class="circle-content">
    <%= render @circle %>
  </div>
<% end %>
```

## Testing Structure

**Test Pyramid** (70/20/10 ratio):
- 70% Unit tests (models, services, jobs)
- 20% Integration tests (controllers, API)
- 10% System tests (full user flows)

**Factory Bot** for test data:
```ruby
FactoryBot.define do
  factory :user do
    phone_number { Faker::PhoneNumber.cell_phone }
    verified { true }
  end
end
```

**RSpec structure**:
```ruby
RSpec.describe Circles::VotingService do
  describe '#cast_vote' do
    subject(:service) { described_class.new(proposal, voter) }

    let(:proposal) { create(:proposal) }
    let(:voter) { create(:circle_member) }

    context 'when vote is valid' do
      it 'creates a vote record' do
        expect { service.call }.to change(Vote, :count).by(1)
      end

      it 'updates proposal status if quorum reached' do
        # Test implementation
      end
    end
  end
end
```

## African Market Specifics

**Mobile Money Integration**:
- M-Pesa (Kenya)
- MTN Mobile Money (Ghana, Uganda)
- Airtel Money (multiple countries)

**Multi-Currency Support** (via Money-Rails):
- KES (Kenyan Shilling)
- NGN (Nigerian Naira)
- GHS (Ghanaian Cedi)
- TZS (Tanzanian Shilling)
- UGX (Ugandan Shilling)

**Feature Phone Support**:
- USSD integration (*384#)
- SMS-based OTP authentication
- WhatsApp Business API

## CI/CD Pipeline

GitHub Actions workflow (`.github/workflows/ci.yml`):
1. **Security Scans**: Brakeman (Rails), bundler-audit (gems), importmap audit (JS)
2. **Lint**: RuboCop style enforcement
3. **Tests**: Full test suite with PostgreSQL
4. **System Tests**: Browser-based tests with screenshot capture on failure

All checks must pass before PR merge.

## Key Configuration Files

- `config/database.yml` - Multi-database setup (primary, cache, queue, cable)
- `Gemfile` - Rails 8 with Solid gems (solid_cache, solid_queue, solid_cable)
- `.github/workflows/ci.yml` - CI pipeline (note: Redis commented out)
- `DEVELOPMENT_GUIDE.md` - Comprehensive setup and patterns guide

## Common Gotchas

1. **No Redis**: Don't suggest Redis/Sidekiq - use Solid alternatives
2. **Passwordless Auth**: No password fields - use OTP flow
3. **Domain Structure**: Code goes in `app/domains/`, not flat `app/models/`
4. **TDD Required**: Tests before code, always
5. **Git Branch Protection**: Always PR to `dev`, never direct commit to `main`
6. **Multi-Currency**: Use Money-Rails, never raw decimal for money
7. **Authorization**: Every action needs `authorize` call (Pundit)

## Documentation References

- **DEVELOPMENT_GUIDE.md**: Complete TDD workflow, Git practices, Rails 8 config
- **claude-project-docs.md**: Full PRD, user personas, feature requirements
- **buystep-commerce-setup.sh**: Original setup script (historical reference)
