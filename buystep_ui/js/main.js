// Main JavaScript for Ubuntu Commerce Network

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuButton = document.querySelector('button.md\\:hidden');
    if (mobileMenuButton) {
        const mobileMenu = document.createElement('div');
        mobileMenu.className = 'mobile-menu hidden';
        mobileMenu.innerHTML = `
            <div class="mobile-menu-content">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <span class="text-primary-600 text-xl font-display font-bold">Ubuntu</span>
                        <span class="text-gray-700 text-xl font-display font-medium ml-1">Commerce</span>
                    </div>
                    <button class="text-gray-700 focus:outline-none close-menu">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <nav class="space-y-4">
                    <a href="index.html" class="block text-gray-700 hover:text-primary-600 font-medium py-2">Home</a>
                    <a href="pages/about.html" class="block text-gray-700 hover:text-primary-600 font-medium py-2">About</a>
                    <a href="pages/how-it-works.html" class="block text-gray-700 hover:text-primary-600 font-medium py-2">How It Works</a>
                    <a href="pages/contact.html" class="block text-gray-700 hover:text-primary-600 font-medium py-2">Contact</a>
                    <div class="border-t border-gray-200 my-4 pt-4">
                        <a href="pages/auth/login.html" class="block text-primary-600 hover:text-primary-700 font-medium py-2">Login</a>
                        <a href="pages/auth/signup.html" class="block bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors mt-2 text-center">Sign Up</a>
                    </div>
                </nav>
            </div>
        `;
        document.body.appendChild(mobileMenu);

        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.remove('hidden');
        });

        const closeMenuButton = mobileMenu.querySelector('.close-menu');
        if (closeMenuButton) {
            closeMenuButton.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
            });
        }
    }

    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    
                    // Create or update error message
                    let errorMessage = field.nextElementSibling;
                    if (!errorMessage || !errorMessage.classList.contains('form-error')) {
                        errorMessage = document.createElement('p');
                        errorMessage.className = 'form-error';
                        field.parentNode.insertBefore(errorMessage, field.nextSibling);
                    }
                    errorMessage.textContent = 'This field is required';
                    
                    // Add error styling
                    field.classList.add('border-red-500');
                    field.classList.add('focus:border-red-500');
                    field.classList.add('focus:ring-red-500');
                } else {
                    // Remove error styling if field is valid
                    field.classList.remove('border-red-500');
                    field.classList.remove('focus:border-red-500');
                    field.classList.remove('focus:ring-red-500');
                    
                    // Remove error message if it exists
                    const errorMessage = field.nextElementSibling;
                    if (errorMessage && errorMessage.classList.contains('form-error')) {
                        errorMessage.textContent = '';
                    }
                }
            });
            
            if (!isValid) {
                event.preventDefault();
            }
        });
    });

    // Animated counters
    const counters = document.querySelectorAll('.counter');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 1500; // ms
        const step = Math.ceil(target / (duration / 16)); // 60fps
        
        let current = 0;
        const updateCounter = () => {
            current += step;
            if (current < target) {
                counter.textContent = current.toLocaleString();
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target.toLocaleString();
            }
        };
        
        // Start animation when element is in viewport
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCounter();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(counter);
    });

    // Circle progress indicators
    const circleProgressElements = document.querySelectorAll('.circle-progress');
    circleProgressElements.forEach(element => {
        const percentage = parseInt(element.getAttribute('data-percentage'));
        element.style.setProperty('--percentage', percentage);
    });

    // Tabs
    const tabGroups = document.querySelectorAll('.tab-group');
    tabGroups.forEach(group => {
        const tabs = group.querySelectorAll('.tab');
        const tabContents = group.querySelectorAll('.tab-content');
        
        tabs.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                // Deactivate all tabs
                tabs.forEach(t => t.classList.remove('active', 'border-primary-500', 'text-primary-600'));
                tabs.forEach(t => t.classList.add('border-transparent', 'text-gray-500'));
                
                // Activate clicked tab
                tab.classList.add('active', 'border-primary-500', 'text-primary-600');
                tab.classList.remove('border-transparent', 'text-gray-500');
                
                // Hide all tab contents
                tabContents.forEach(content => content.classList.add('hidden'));
                
                // Show corresponding content
                tabContents[index].classList.remove('hidden');
            });
        });
    });

    // Dropdowns
    const dropdownButtons = document.querySelectorAll('.dropdown-button');
    dropdownButtons.forEach(button => {
        const dropdown = button.nextElementSibling;
        
        button.addEventListener('click', () => {
            dropdown.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            if (!button.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });
    });

    // Modals
    const modalTriggers = document.querySelectorAll('[data-modal-target]');
    const modalCloseButtons = document.querySelectorAll('[data-modal-close]');
    
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', () => {
            const modalId = trigger.getAttribute('data-modal-target');
            const modal = document.getElementById(modalId);
            modal.classList.remove('hidden');
        });
    });
    
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            modal.classList.add('hidden');
        });
    });

    // Close modals when clicking outside content
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        });
    });

    // Alerts dismissal
    const alertCloseButtons = document.querySelectorAll('.alert .close');
    alertCloseButtons.forEach(button => {
        button.addEventListener('click', () => {
            const alert = button.closest('.alert');
            alert.classList.add('opacity-0');
            setTimeout(() => {
                alert.remove();
            }, 300);
        });
    });
});


// Responsive Design Functions

// Create mobile bottom navigation
function createMobileBottomNav() {
    // Check if it already exists
    if (document.querySelector('.mobile-bottom-nav')) {
        return;
    }
    
    const nav = document.createElement('div');
    nav.className = 'mobile-bottom-nav';
    
    // Determine current page
    const currentPath = window.location.pathname;
    
    // Create navigation items
    const navItems = [
        { icon: 'fa-home', text: 'Home', path: '/index.html' },
        { icon: 'fa-users', text: 'Circles', path: '/pages/circle/my-circles.html' },
        { icon: 'fa-exchange-alt', text: 'Transactions', path: '/pages/transaction/transactions.html' },
        { icon: 'fa-store', text: 'Merchants', path: '/pages/merchant/merchants.html' },
        { icon: 'fa-user', text: 'Profile', path: '/pages/dashboard/profile.html' }
    ];
    
    navItems.forEach(item => {
        const link = document.createElement('a');
        link.href = item.path;
        link.className = currentPath.includes(item.path) ? 'active' : '';
        
        const icon = document.createElement('i');
        icon.className = `fas ${item.icon}`;
        
        const span = document.createElement('span');
        span.textContent = item.text;
        
        link.appendChild(icon);
        link.appendChild(span);
        nav.appendChild(link);
    });
    
    document.body.appendChild(nav);
}

// Handle responsive sidebar
function setupResponsiveSidebar() {
    const mobileMenuButton = document.querySelector('button.md\\:hidden');
    const sidebar = document.querySelector('aside');
    
    if (mobileMenuButton && sidebar) {
        mobileMenuButton.addEventListener('click', function() {
            sidebar.classList.toggle('hidden');
            sidebar.classList.toggle('fixed');
            sidebar.classList.toggle('inset-0');
            sidebar.classList.toggle('z-40');
            
            // Create or toggle overlay
            let overlay = document.querySelector('.sidebar-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'sidebar-overlay';
                document.body.appendChild(overlay);
                
                // Close sidebar when clicking overlay
                overlay.addEventListener('click', function() {
                    sidebar.classList.add('hidden');
                    sidebar.classList.remove('fixed', 'inset-0', 'z-40');
                    overlay.classList.remove('active');
                });
            }
            
            if (!sidebar.classList.contains('hidden')) {
                overlay.classList.add('active');
            } else {
                overlay.classList.remove('active');
            }
        });
    }
}

// Show toast notification
function showToast(message, type = 'info', duration = 3000) {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast');
    existingToasts.forEach(toast => toast.remove());
    
    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.style.position = 'fixed';
    toast.style.top = '1rem';
    toast.style.right = '1rem';
    toast.style.padding = '1rem';
    toast.style.backgroundColor = 'white';
    toast.style.borderRadius = '0.375rem';
    toast.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
    toast.style.zIndex = '50';
    toast.style.opacity = '0';
    toast.style.transform = 'translateY(-1rem)';
    toast.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
    
    // Add border based on type
    switch (type) {
        case 'success':
            toast.style.borderLeft = '4px solid #16a34a';
            break;
        case 'error':
            toast.style.borderLeft = '4px solid #dc2626';
            break;
        case 'warning':
            toast.style.borderLeft = '4px solid #f59e0b';
            break;
        default:
            toast.style.borderLeft = '4px solid #3b82f6';
    }
    
    const icon = document.createElement('i');
    
    switch (type) {
        case 'success':
            icon.className = 'fas fa-check-circle text-green-500 mr-2';
            break;
        case 'error':
            icon.className = 'fas fa-exclamation-circle text-red-500 mr-2';
            break;
        case 'warning':
            icon.className = 'fas fa-exclamation-triangle text-yellow-500 mr-2';
            break;
        default:
            icon.className = 'fas fa-info-circle text-blue-500 mr-2';
    }
    
    const content = document.createElement('div');
    content.className = 'flex items-center';
    content.appendChild(icon);
    content.appendChild(document.createTextNode(message));
    
    const closeButton = document.createElement('button');
    closeButton.className = 'ml-4 text-gray-400 hover:text-gray-600';
    closeButton.innerHTML = '<i class="fas fa-times"></i>';
    closeButton.addEventListener('click', () => toast.remove());
    
    toast.appendChild(content);
    toast.appendChild(closeButton);
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 10);
    
    // Hide toast after duration
    if (duration) {
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(-1rem)';
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }
    
    return toast;
}

// Make tables responsive
function makeTablesResponsive() {
    const tables = document.querySelectorAll('table:not(.responsive-table)');
    tables.forEach(table => {
        const wrapper = document.createElement('div');
        wrapper.className = 'responsive-table';
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    });
}

// Initialize responsive features
document.addEventListener('DOMContentLoaded', function() {
    // Add mobile bottom navigation on small screens
    if (window.innerWidth <= 768) {
        createMobileBottomNav();
    }
    
    // Setup responsive sidebar
    setupResponsiveSidebar();
    
    // Make tables responsive
    makeTablesResponsive();
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768 && !document.querySelector('.mobile-bottom-nav')) {
            createMobileBottomNav();
        } else if (window.innerWidth > 768 && document.querySelector('.mobile-bottom-nav')) {
            document.querySelector('.mobile-bottom-nav').remove();
        }
    });
});

// Utility functions
function formatCurrency(amount, currency = 'KES') {
    return `${currency} ${parseFloat(amount).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })}`;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
}

function truncateText(text, length = 50) {
    if (text.length <= length) return text;
    return text.substring(0, length) + '...';
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(
        () => showToast('Copied to clipboard!', 'success'),
        () => showToast('Failed to copy to clipboard', 'error')
    );
}

function debounce(func, wait = 300) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}
