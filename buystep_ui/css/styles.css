/* Custom styles for Ubuntu Commerce Network */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom focus styles */
:focus {
    outline: 2px solid rgba(34, 197, 94, 0.5);
    outline-offset: 2px;
}

/* Custom animations */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom card hover effect */
.hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom button styles */
.btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-secondary {
    @apply bg-transparent border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-accent {
    @apply bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-md font-medium transition-colors;
}

/* Custom form styles */
.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error {
    @apply text-red-500 text-sm mt-1;
}

/* Custom badge styles */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply bg-primary-100 text-primary-800;
}

.badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
}

.badge-accent {
    @apply bg-accent-100 text-accent-800;
}

/* Custom card styles */
.card {
    @apply bg-white rounded-lg shadow-sm overflow-hidden;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Custom table styles */
.table-container {
    @apply overflow-x-auto;
}

.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table th {
    @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap;
}

.table tr:nth-child(even) {
    @apply bg-gray-50;
}

/* Custom alert styles */
.alert {
    @apply p-4 rounded-md mb-4;
}

.alert-success {
    @apply bg-green-50 text-green-800 border border-green-200;
}

.alert-warning {
    @apply bg-yellow-50 text-yellow-800 border border-yellow-200;
}

.alert-error {
    @apply bg-red-50 text-red-800 border border-red-200;
}

.alert-info {
    @apply bg-blue-50 text-blue-800 border border-blue-200;
}

/* Mobile menu styles */
.mobile-menu {
    @apply fixed inset-0 bg-gray-800 bg-opacity-75 z-50 transform transition-transform duration-300 ease-in-out;
}

.mobile-menu.hidden {
    @apply -translate-x-full;
}

.mobile-menu-content {
    @apply bg-white h-full w-4/5 max-w-xs py-6 px-4 overflow-y-auto;
}

/* Circle progress indicator */
.circle-progress {
    @apply relative w-20 h-20;
}

.circle-progress svg {
    @apply transform -rotate-90;
}

.circle-progress circle {
    @apply stroke-current text-gray-200;
    stroke-width: 8;
    fill: transparent;
}

.circle-progress circle:nth-child(2) {
    @apply text-primary-500;
    stroke-dasharray: 251.2;
    stroke-dashoffset: calc(251.2 - (251.2 * var(--percentage)) / 100);
    transition: stroke-dashoffset 1s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}


/* Responsive Design Styles */

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    display: none;
}

@media (max-width: 768px) {
    .mobile-bottom-nav {
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
        z-index: 30;
        height: 4rem;
        padding: 0.5rem 0;
    }
    
    .mobile-bottom-nav a {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        font-size: 0.75rem;
    }
    
    .mobile-bottom-nav a.active {
        color: #16a34a;
    }
    
    .mobile-bottom-nav i {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }
    
    /* Add padding to main content to account for bottom nav */
    main {
        padding-bottom: 5rem;
    }
}

/* Responsive Typography */
@media (max-width: 640px) {
    h1 {
        font-size: 1.5rem !important;
    }
    h2 {
        font-size: 1.25rem !important;
    }
    h3 {
        font-size: 1.125rem !important;
    }
    .text-2xl {
        font-size: 1.25rem !important;
    }
    .text-xl {
        font-size: 1.125rem !important;
    }
}

/* Responsive Tables */
@media (max-width: 768px) {
    .responsive-table {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .responsive-card-table tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .responsive-card-table td {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e5e7eb;
        padding: 0.75rem 1rem;
    }
    
    .responsive-card-table td:last-child {
        border-bottom: none;
    }
    
    .responsive-card-table td:before {
        content: attr(data-label);
        font-weight: 500;
        color: #4b5563;
    }
    
    .responsive-card-table thead {
        display: none;
    }
}

/* Responsive Grid Adjustments */
@media (max-width: 640px) {
    .sm\:grid-cols-2 {
        grid-template-columns: 1fr !important;
    }
    
    .sm\:grid-cols-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
}

/* Responsive Sidebar */
@media (max-width: 768px) {
    aside.fixed {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        z-index: 50;
        background-color: white;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }
    
    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

/* Toast Notifications for Mobile */
@media (max-width: 640px) {
    .toast {
        left: 1rem;
        right: 1rem;
        width: auto;
    }
}

/* Print Styles */
@media print {
    header, aside, footer, .no-print, .mobile-bottom-nav {
        display: none !important;
    }
    
    body, main {
        background-color: white !important;
        color: black !important;
    }
    
    .print-break-inside-avoid {
        break-inside: avoid;
    }
    
    a {
        text-decoration: none !important;
        color: #000 !important;
    }
}
