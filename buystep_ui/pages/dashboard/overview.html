<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Ubuntu Commerce Network</title>
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        secondary: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                            950: '#431407',
                        },
                        accent: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                            950: '#2e1065',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        display: ['Montserrat', 'sans-serif'],
                    },
                },
            },
        }
    </script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../css/styles.css">
</head>
<body class="bg-gray-50 font-sans">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <aside class="hidden md:flex md:flex-col w-64 bg-white border-r border-gray-200">
            <div class="flex items-center justify-center h-16 border-b border-gray-200">
                <a href="../../index.html" class="flex items-center">
                    <span class="text-primary-600 text-xl font-display font-bold">Ubuntu</span>
                    <span class="text-gray-700 text-xl font-display font-medium ml-1">Commerce</span>
                </a>
            </div>
            <div class="flex flex-col flex-grow p-4 overflow-y-auto">
                <nav class="flex-1 space-y-1">
                    <a href="overview.html" class="flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md group">
                        <i class="fas fa-home mr-3 text-white"></i>
                        Dashboard
                    </a>
                    <a href="../circle/my-circles.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-users mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        My Circles
                    </a>
                    <a href="../transaction/transactions.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-exchange-alt mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Transactions
                    </a>
                    <a href="wallet.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-wallet mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Wallet
                    </a>
                    <a href="credit-score.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-chart-line mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Credit Score
                    </a>
                    <a href="../merchant/merchants.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-store mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Merchants
                    </a>
                    <a href="../group/buying-groups.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-shopping-cart mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Group Buying
                    </a>
                    <a href="profile.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-user mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Profile
                    </a>
                    <a href="settings.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-cog mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Settings
                    </a>
                </nav>
                <div class="mt-auto">
                    <a href="../auth/login.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-red-600 rounded-md group">
                        <i class="fas fa-sign-out-alt mr-3 text-gray-500 group-hover:text-red-600"></i>
                        Logout
                    </a>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm z-10">
                <div class="px-4 sm:px-6 lg:px-8 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <button class="md:hidden text-gray-700 focus:outline-none mr-3">
                                <i class="fas fa-bars text-xl"></i>
                            </button>
                            <h1 class="text-lg font-medium text-gray-900">Dashboard</h1>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button class="text-gray-500 hover:text-gray-700 focus:outline-none">
                                <i class="fas fa-bell text-xl"></i>
                                <span class="absolute top-3 right-3 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">3</span>
                            </button>
                            <div class="relative">
                                <button class="flex items-center focus:outline-none dropdown-button">
                                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">JD</span>
                                    </div>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden sm:block">John Doe</span>
                                    <i class="fas fa-chevron-down ml-1 text-xs text-gray-500"></i>
                                </button>
                                <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                                    <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                                    <a href="settings.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                                    <div class="border-t border-gray-100"></div>
                                    <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6 lg:p-8">
                <!-- Welcome Banner -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                            <h2 class="text-2xl font-display font-bold text-gray-900">Welcome back, John!</h2>
                            <p class="mt-1 text-gray-600">Here's what's happening with your account today.</p>
                        </div>
                        <div class="mt-4 md:mt-0 flex space-x-3">
                            <a href="../transaction/send-money.html" class="btn-primary flex items-center">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Money
                            </a>
                            <a href="../transaction/request-money.html" class="btn-secondary flex items-center">
                                <i class="fas fa-hand-holding-usd mr-2"></i>
                                Request Money
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <!-- Wallet Balance -->
                    <div class="bg-white rounded-lg shadow-sm p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Wallet Balance</p>
                                <p class="text-2xl font-bold text-gray-900">KES 12,500</p>
                            </div>
                            <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-wallet text-primary-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="wallet.html" class="text-sm font-medium text-primary-600 hover:text-primary-700 flex items-center">
                                View Details
                                <i class="fas fa-arrow-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Credit Score -->
                    <div class="bg-white rounded-lg shadow-sm p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Credit Score</p>
                                <p class="text-2xl font-bold text-gray-900">75/100</p>
                            </div>
                            <div class="w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-line text-accent-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-accent-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <a href="credit-score.html" class="text-sm font-medium text-accent-600 hover:text-accent-700 flex items-center">
                                View Details
                                <i class="fas fa-arrow-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Active Circles -->
                    <div class="bg-white rounded-lg shadow-sm p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Active Circles</p>
                                <p class="text-2xl font-bold text-gray-900">3</p>
                            </div>
                            <div class="w-12 h-12 bg-secondary-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-secondary-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="../circle/my-circles.html" class="text-sm font-medium text-secondary-600 hover:text-secondary-700 flex items-center">
                                View Circles
                                <i class="fas fa-arrow-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Available Credit -->
                    <div class="bg-white rounded-lg shadow-sm p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Available Credit</p>
                                <p class="text-2xl font-bold text-gray-900">KES 45,000</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-credit-card text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="../transaction/pay-merchant.html" class="text-sm font-medium text-green-600 hover:text-green-700 flex items-center">
                                Use Credit
                                <i class="fas fa-arrow-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions & Circle Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Recent Transactions -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
                            <a href="../transaction/transactions.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">View All</a>
                        </div>
                        <div class="space-y-4">
                            <!-- Transaction 1 -->
                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-arrow-down text-green-600"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">Received from Sarah</p>
                                    <p class="text-xs text-gray-500">Today, 10:30 AM</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-green-600">+KES 2,500</p>
                                    <p class="text-xs text-gray-500">Circle: Family</p>
                                </div>
                            </div>
                            
                            <!-- Transaction 2 -->
                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-arrow-up text-red-600"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">Paid to Supermarket</p>
                                    <p class="text-xs text-gray-500">Yesterday, 4:15 PM</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-red-600">-KES 1,850</p>
                                    <p class="text-xs text-gray-500">Credit Used</p>
                                </div>
                            </div>
                            
                            <!-- Transaction 3 -->
                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-sync-alt text-blue-600"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">Circle Contribution</p>
                                    <p class="text-xs text-gray-500">May 15, 9:00 AM</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-blue-600">-KES 500</p>
                                    <p class="text-xs text-gray-500">Circle: Neighborhood</p>
                                </div>
                            </div>
                            
                            <!-- Transaction 4 -->
                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-shopping-bag text-purple-600"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">Group Purchase: Fertilizer</p>
                                    <p class="text-xs text-gray-500">May 12, 2:30 PM</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-purple-600">-KES 3,200</p>
                                    <p class="text-xs text-gray-500">Group: Farmers Co-op</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Circle Activity -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Circle Activity</h3>
                            <a href="../circle/my-circles.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">View All</a>
                        </div>
                        
                        <!-- Circle Stats -->
                        <div class="mb-6">
                            <canvas id="circleContributionsChart" height="200"></canvas>
                        </div>
                        
                        <!-- Circle Updates -->
                        <div class="space-y-4">
                            <!-- Update 1 -->
                            <div class="flex items-start p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-bell text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">New loan request in Family Circle</p>
                                    <p class="text-xs text-gray-500">Sarah needs KES 5,000 for school fees</p>
                                    <div class="mt-2 flex space-x-2">
                                        <button class="px-3 py-1 bg-primary-600 text-white text-xs rounded-md hover:bg-primary-700">Approve</button>
                                        <button class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300">Decline</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Update 2 -->
                            <div class="flex items-start p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Neighborhood Circle contribution due</p>
                                    <p class="text-xs text-gray-500">Your monthly contribution of KES 500 is due in 2 days</p>
                                    <div class="mt-2">
                                        <button class="px-3 py-1 bg-primary-600 text-white text-xs rounded-md hover:bg-primary-700">Pay Now</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Merchant Offers & Credit Score -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Merchant Offers -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Merchant Offers</h3>
                            <a href="../merchant/merchants.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">View All</a>
                        </div>
                        <div class="space-y-4">
                            <!-- Offer 1 -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gray-200 rounded-md flex-shrink-0"></div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Supermarket Discount</h4>
                                        <p class="text-xs text-gray-500">Get 10% off your next purchase</p>
                                    </div>
                                    <div class="ml-auto">
                                        <span class="badge badge-primary">New</span>
                                    </div>
                                </div>
                                <div class="mt-3 flex justify-end">
                                    <a href="#" class="text-xs font-medium text-primary-600 hover:text-primary-700">View Details</a>
                                </div>
                            </div>
                            
                            <!-- Offer 2 -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gray-200 rounded-md flex-shrink-0"></div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Electronics Store</h4>
                                        <p class="text-xs text-gray-500">Buy now, pay in 3 installments</p>
                                    </div>
                                </div>
                                <div class="mt-3 flex justify-end">
                                    <a href="#" class="text-xs font-medium text-primary-600 hover:text-primary-700">View Details</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Credit Score Improvement -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Improve Your Credit Score</h3>
                            <a href="credit-score.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">View Details</a>
                        </div>
                        <div class="space-y-4">
                            <!-- Tip 1 -->
                            <div class="flex items-start">
                                <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 flex-shrink-0">
                                    <i class="fas fa-check text-green-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Make regular circle contributions</p>
                                    <p class="text-xs text-gray-500">You've been consistent with your contributions!</p>
                                </div>
                            </div>
                            
                            <!-- Tip 2 -->
                            <div class="flex items-start">
                                <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3 flex-shrink-0">
                                    <i class="fas fa-exclamation text-yellow-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Repay your current loan on time</p>
                                    <p class="text-xs text-gray-500">You have a payment due in 5 days</p>
                                </div>
                            </div>
                            
                            <!-- Tip 3 -->
                            <div class="flex items-start">
                                <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                                    <i class="fas fa-plus text-gray-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Join another circle to increase your network</p>
                                    <p class="text-xs text-gray-500">More circles = higher credit limit</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../../js/main.js"></script>
    <script>
        // Circle Contributions Chart
        const circleCtx = document.getElementById('circleContributionsChart').getContext('2d');
        const circleChart = new Chart(circleCtx, {
            type: 'bar',
            data: {
                labels: ['Family', 'Neighborhood', 'Work'],
                datasets: [{
                    label: 'Your Contribution',
                    data: [2500, 1500, 3000],
                    backgroundColor: '#16a34a',
                    borderColor: '#16a34a',
                    borderWidth: 1
                }, {
                    label: 'Circle Total',
                    data: [12500, 8000, 15000],
                    backgroundColor: '#a78bfa',
                    borderColor: '#a78bfa',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount (KES)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Circle Contributions'
                    }
                }
            }
        });
        
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('button.md\\:hidden');
        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', function() {
                const sidebar = document.querySelector('aside');
                sidebar.classList.toggle('hidden');
                sidebar.classList.toggle('fixed');
                sidebar.classList.toggle('inset-0');
                sidebar.classList.toggle('z-40');
                sidebar.classList.toggle('md:relative');
                sidebar.classList.toggle('md:flex');
            });
        }
        
        // Dropdown toggle
        const dropdownButton = document.querySelector('.dropdown-button');
        const dropdown = document.querySelector('.dropdown-button + div');
        
        if (dropdownButton && dropdown) {
            dropdownButton.addEventListener('click', function() {
                dropdown.classList.toggle('hidden');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!dropdownButton.contains(event.target) && !dropdown.contains(event.target)) {
                    dropdown.classList.add('hidden');
                }
            });
        }
    </script>
</body>
</html>
