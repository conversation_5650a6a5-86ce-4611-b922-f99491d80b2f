<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Circles - Ubuntu Commerce Network</title>
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        secondary: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                            950: '#431407',
                        },
                        accent: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                            950: '#2e1065',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        display: ['Montserrat', 'sans-serif'],
                    },
                },
            },
        }
    </script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../css/styles.css">
</head>
<body class="bg-gray-50 font-sans">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <aside class="hidden md:flex md:flex-col w-64 bg-white border-r border-gray-200">
            <div class="flex items-center justify-center h-16 border-b border-gray-200">
                <a href="../../index.html" class="flex items-center">
                    <span class="text-primary-600 text-xl font-display font-bold">Ubuntu</span>
                    <span class="text-gray-700 text-xl font-display font-medium ml-1">Commerce</span>
                </a>
            </div>
            <div class="flex flex-col flex-grow p-4 overflow-y-auto">
                <nav class="flex-1 space-y-1">
                    <a href="../dashboard/overview.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-home mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Dashboard
                    </a>
                    <a href="my-circles.html" class="flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md group">
                        <i class="fas fa-users mr-3 text-white"></i>
                        My Circles
                    </a>
                    <a href="../transaction/transactions.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-exchange-alt mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Transactions
                    </a>
                    <a href="../dashboard/wallet.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-wallet mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Wallet
                    </a>
                    <a href="../dashboard/credit-score.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-chart-line mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Credit Score
                    </a>
                    <a href="../merchant/merchants.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-store mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Merchants
                    </a>
                    <a href="../group/buying-groups.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-shopping-cart mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Group Buying
                    </a>
                    <a href="../dashboard/profile.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-user mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Profile
                    </a>
                    <a href="../dashboard/settings.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-md group">
                        <i class="fas fa-cog mr-3 text-gray-500 group-hover:text-primary-600"></i>
                        Settings
                    </a>
                </nav>
                <div class="mt-auto">
                    <a href="../auth/login.html" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-red-600 rounded-md group">
                        <i class="fas fa-sign-out-alt mr-3 text-gray-500 group-hover:text-red-600"></i>
                        Logout
                    </a>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm z-10">
                <div class="px-4 sm:px-6 lg:px-8 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <button class="md:hidden text-gray-700 focus:outline-none mr-3">
                                <i class="fas fa-bars text-xl"></i>
                            </button>
                            <h1 class="text-lg font-medium text-gray-900">My Circles</h1>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button class="text-gray-500 hover:text-gray-700 focus:outline-none">
                                <i class="fas fa-bell text-xl"></i>
                                <span class="absolute top-3 right-3 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">3</span>
                            </button>
                            <div class="relative">
                                <button class="flex items-center focus:outline-none dropdown-button">
                                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">JD</span>
                                    </div>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden sm:block">John Doe</span>
                                    <i class="fas fa-chevron-down ml-1 text-xs text-gray-500"></i>
                                </button>
                                <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                                    <a href="../dashboard/profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                                    <a href="../dashboard/settings.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                                    <div class="border-t border-gray-100"></div>
                                    <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6 lg:p-8">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h2 class="text-2xl font-display font-bold text-gray-900">My Community Guarantee Circles</h2>
                        <p class="mt-1 text-gray-600">Manage your circles, contributions, and loan requests</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <a href="create-circle.html" class="btn-primary flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            Create New Circle
                        </a>
                    </div>
                </div>

                <!-- Circle Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Total Circles -->
                    <div class="bg-white rounded-lg shadow-sm p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Total Circles</p>
                                <p class="text-2xl font-bold text-gray-900">3</p>
                            </div>
                            <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-primary-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">You're a member of 3 active circles</p>
                        </div>
                    </div>

                    <!-- Total Contributions -->
                    <div class="bg-white rounded-lg shadow-sm p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Total Contributions</p>
                                <p class="text-2xl font-bold text-gray-900">KES 7,000</p>
                            </div>
                            <div class="w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-hand-holding-usd text-accent-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">Your total contributions across all circles</p>
                        </div>
                    </div>

                    <!-- Available Credit -->
                    <div class="bg-white rounded-lg shadow-sm p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Available Credit</p>
                                <p class="text-2xl font-bold text-gray-900">KES 45,000</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-credit-card text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">Based on your circle contributions and credit score</p>
                        </div>
                    </div>
                </div>

                <!-- Circle Tabs -->
                <div class="bg-white rounded-lg shadow-sm mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="flex -mb-px tab-group">
                            <button class="tab text-primary-600 border-primary-500 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm active">
                                My Circles
                            </button>
                            <button class="tab text-gray-500 border-transparent whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm">
                                Pending Invitations
                                <span class="ml-2 bg-red-100 text-red-600 py-0.5 px-2 rounded-full text-xs">2</span>
                            </button>
                            <button class="tab text-gray-500 border-transparent whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm">
                                Loan Requests
                                <span class="ml-2 bg-yellow-100 text-yellow-600 py-0.5 px-2 rounded-full text-xs">1</span>
                            </button>
                        </nav>
                    </div>
                    
                    <!-- My Circles Tab Content -->
                    <div class="tab-content p-6">
                        <div class="space-y-6">
                            <!-- Circle 1: Family Circle -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-300 transition-colors">
                                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4">
                                                <i class="fas fa-home text-primary-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">Family Circle</h3>
                                                <p class="text-sm text-gray-500">Created by you • 8 members</p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-primary">Admin</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-4">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Your Contribution</p>
                                            <p class="text-xl font-bold text-gray-900">KES 2,500</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Circle Total</p>
                                            <p class="text-xl font-bold text-gray-900">KES 12,500</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Your Credit Limit</p>
                                            <p class="text-xl font-bold text-green-600">KES 20,000</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-primary-600 h-2 rounded-full" style="width: 75%"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Circle Health: 75% (Good)</p>
                                    </div>
                                </div>
                                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <a href="circle-details.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">View Details</a>
                                        <span class="text-gray-300">|</span>
                                        <a href="make-contribution.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">Make Contribution</a>
                                    </div>
                                    <div>
                                        <button class="px-3 py-1 bg-primary-600 text-white text-xs rounded-md hover:bg-primary-700">Request Loan</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Circle 2: Neighborhood Circle -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-300 transition-colors">
                                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-secondary-100 flex items-center justify-center mr-4">
                                                <i class="fas fa-map-marker-alt text-secondary-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">Neighborhood Circle</h3>
                                                <p class="text-sm text-gray-500">Created by Sarah M. • 12 members</p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-secondary">Member</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-4">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Your Contribution</p>
                                            <p class="text-xl font-bold text-gray-900">KES 1,500</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Circle Total</p>
                                            <p class="text-xl font-bold text-gray-900">KES 8,000</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Your Credit Limit</p>
                                            <p class="text-xl font-bold text-green-600">KES 12,000</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-secondary-600 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Circle Health: 85% (Excellent)</p>
                                    </div>
                                    <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-3">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-exclamation-circle text-yellow-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-yellow-700">Your monthly contribution of KES 500 is due in 2 days</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <a href="circle-details.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">View Details</a>
                                        <span class="text-gray-300">|</span>
                                        <a href="make-contribution.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">Make Contribution</a>
                                    </div>
                                    <div>
                                        <button class="px-3 py-1 bg-primary-600 text-white text-xs rounded-md hover:bg-primary-700">Request Loan</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Circle 3: Work Circle -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-300 transition-colors">
                                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-accent-100 flex items-center justify-center mr-4">
                                                <i class="fas fa-briefcase text-accent-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">Work Circle</h3>
                                                <p class="text-sm text-gray-500">Created by David O. • 6 members</p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-accent">Member</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-4">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Your Contribution</p>
                                            <p class="text-xl font-bold text-gray-900">KES 3,000</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Circle Total</p>
                                            <p class="text-xl font-bold text-gray-900">KES 15,000</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Your Credit Limit</p>
                                            <p class="text-xl font-bold text-green-600">KES 18,000</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-accent-600 h-2 rounded-full" style="width: 90%"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Circle Health: 90% (Excellent)</p>
                                    </div>
                                </div>
                                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <a href="circle-details.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">View Details</a>
                                        <span class="text-gray-300">|</span>
                                        <a href="make-contribution.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">Make Contribution</a>
                                    </div>
                                    <div>
                                        <button class="px-3 py-1 bg-primary-600 text-white text-xs rounded-md hover:bg-primary-700">Request Loan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pending Invitations Tab Content -->
                    <div class="tab-content p-6 hidden">
                        <div class="space-y-6">
                            <!-- Invitation 1 -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-300 transition-colors">
                                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                                <i class="fas fa-store text-blue-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">Market Vendors Circle</h3>
                                                <p class="text-sm text-gray-500">Invited by Mary W. • 15 members</p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-primary">New</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-4">
                                    <p class="text-sm text-gray-700">
                                        You've been invited to join the Market Vendors Circle. This circle is for local market vendors to support each other with inventory financing and business growth.
                                    </p>
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-700">Circle Details:</p>
                                        <ul class="mt-2 space-y-1 text-sm text-gray-600">
                                            <li class="flex items-center">
                                                <i class="fas fa-circle text-xs mr-2 text-gray-400"></i>
                                                Monthly contribution: KES 1,000
                                            </li>
                                            <li class="flex items-center">
                                                <i class="fas fa-circle text-xs mr-2 text-gray-400"></i>
                                                Current circle total: KES 12,000
                                            </li>
                                            <li class="flex items-center">
                                                <i class="fas fa-circle text-xs mr-2 text-gray-400"></i>
                                                Circle health: 80% (Good)
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                                    <button class="px-4 py-2 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300">Decline</button>
                                    <button class="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700">Accept Invitation</button>
                                </div>
                            </div>
                            
                            <!-- Invitation 2 -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-300 transition-colors">
                                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-4">
                                                <i class="fas fa-graduation-cap text-purple-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">School Parents Circle</h3>
                                                <p class="text-sm text-gray-500">Invited by James K. • 20 members</p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-primary">New</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-4">
                                    <p class="text-sm text-gray-700">
                                        You've been invited to join the School Parents Circle. This circle helps parents save for school fees and access emergency education funds when needed.
                                    </p>
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-700">Circle Details:</p>
                                        <ul class="mt-2 space-y-1 text-sm text-gray-600">
                                            <li class="flex items-center">
                                                <i class="fas fa-circle text-xs mr-2 text-gray-400"></i>
                                                Monthly contribution: KES 2,000
                                            </li>
                                            <li class="flex items-center">
                                                <i class="fas fa-circle text-xs mr-2 text-gray-400"></i>
                                                Current circle total: KES 35,000
                                            </li>
                                            <li class="flex items-center">
                                                <i class="fas fa-circle text-xs mr-2 text-gray-400"></i>
                                                Circle health: 95% (Excellent)
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                                    <button class="px-4 py-2 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300">Decline</button>
                                    <button class="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700">Accept Invitation</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Loan Requests Tab Content -->
                    <div class="tab-content p-6 hidden">
                        <div class="space-y-6">
                            <!-- Loan Request 1 -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-300 transition-colors">
                                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4">
                                                <i class="fas fa-home text-primary-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">Family Circle</h3>
                                                <p class="text-sm text-gray-500">Loan request from Sarah</p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-secondary">Pending Approval</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-4">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Amount Requested</p>
                                            <p class="text-xl font-bold text-gray-900">KES 5,000</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Purpose</p>
                                            <p class="text-xl font-bold text-gray-900">School Fees</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Repayment Period</p>
                                            <p class="text-xl font-bold text-gray-900">3 months</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-700">Request Details:</p>
                                        <p class="mt-1 text-sm text-gray-600">
                                            "I need to pay my daughter's school fees for the new term. I will repay in 3 monthly installments of KES 1,667 each."
                                        </p>
                                    </div>
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-700">Approval Status:</p>
                                        <div class="mt-2 flex items-center">
                                            <div class="flex-1">
                                                <div class="w-full bg-gray-200 rounded-full h-2">
                                                    <div class="bg-primary-600 h-2 rounded-full" style="width: 60%"></div>
                                                </div>
                                            </div>
                                            <span class="ml-2 text-sm text-gray-600">3/5 approved</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                                    <button class="px-4 py-2 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300">Decline</button>
                                    <button class="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700">Approve</button>
                                </div>
                            </div>
                            
                            <!-- Your Loan Request -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-300 transition-colors">
                                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-accent-100 flex items-center justify-center mr-4">
                                                <i class="fas fa-briefcase text-accent-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">Work Circle</h3>
                                                <p class="text-sm text-gray-500">Your loan request</p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-accent">Your Request</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-4">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Amount Requested</p>
                                            <p class="text-xl font-bold text-gray-900">KES 10,000</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Purpose</p>
                                            <p class="text-xl font-bold text-gray-900">Business Inventory</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Repayment Period</p>
                                            <p class="text-xl font-bold text-gray-900">2 months</p>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-700">Request Details:</p>
                                        <p class="mt-1 text-sm text-gray-600">
                                            "I need to purchase inventory for my small business. I will repay in 2 monthly installments of KES 5,000 each."
                                        </p>
                                    </div>
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-700">Approval Status:</p>
                                        <div class="mt-2 flex items-center">
                                            <div class="flex-1">
                                                <div class="w-full bg-gray-200 rounded-full h-2">
                                                    <div class="bg-accent-600 h-2 rounded-full" style="width: 83%"></div>
                                                </div>
                                            </div>
                                            <span class="ml-2 text-sm text-gray-600">5/6 approved</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                                    <div>
                                        <p class="text-sm text-gray-600">Waiting for 1 more approval</p>
                                    </div>
                                    <div>
                                        <button class="px-4 py-2 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300">Cancel Request</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Circle Insights -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Circle Insights</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Contribution History Chart -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Your Contribution History</h4>
                            <canvas id="contributionHistoryChart" height="250"></canvas>
                        </div>
                        
                        <!-- Circle Health Comparison -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Circle Health Comparison</h4>
                            <canvas id="circleHealthChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../../js/main.js"></script>
    <script>
        // Contribution History Chart
        const contributionCtx = document.getElementById('contributionHistoryChart').getContext('2d');
        const contributionChart = new Chart(contributionCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [
                    {
                        label: 'Family Circle',
                        data: [500, 500, 500, 500, 500, 0],
                        borderColor: '#16a34a',
                        backgroundColor: 'rgba(22, 163, 74, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Neighborhood Circle',
                        data: [300, 300, 300, 300, 300, 0],
                        borderColor: '#ea580c',
                        backgroundColor: 'rgba(234, 88, 12, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Work Circle',
                        data: [600, 600, 600, 600, 600, 0],
                        borderColor: '#7c3aed',
                        backgroundColor: 'rgba(124, 58, 237, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount (KES)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Circle Health Chart
        const healthCtx = document.getElementById('circleHealthChart').getContext('2d');
        const healthChart = new Chart(healthCtx, {
            type: 'radar',
            data: {
                labels: [
                    'Contribution Rate',
                    'Repayment Rate',
                    'Member Activity',
                    'Circle Growth',
                    'Trust Score'
                ],
                datasets: [
                    {
                        label: 'Family Circle',
                        data: [80, 90, 70, 60, 75],
                        backgroundColor: 'rgba(22, 163, 74, 0.2)',
                        borderColor: '#16a34a',
                        pointBackgroundColor: '#16a34a'
                    },
                    {
                        label: 'Neighborhood Circle',
                        data: [90, 85, 80, 70, 85],
                        backgroundColor: 'rgba(234, 88, 12, 0.2)',
                        borderColor: '#ea580c',
                        pointBackgroundColor: '#ea580c'
                    },
                    {
                        label: 'Work Circle',
                        data: [95, 90, 85, 80, 90],
                        backgroundColor: 'rgba(124, 58, 237, 0.2)',
                        borderColor: '#7c3aed',
                        pointBackgroundColor: '#7c3aed'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: {
                            display: true
                        },
                        suggestedMin: 0,
                        suggestedMax: 100
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Tab switching
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabs.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                // Deactivate all tabs
                tabs.forEach(t => {
                    t.classList.remove('text-primary-600', 'border-primary-500');
                    t.classList.add('text-gray-500', 'border-transparent');
                });
                
                // Activate clicked tab
                tab.classList.remove('text-gray-500', 'border-transparent');
                tab.classList.add('text-primary-600', 'border-primary-500');
                
                // Hide all tab contents
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Show corresponding content
                tabContents[index].classList.remove('hidden');
            });
        });
        
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('button.md\\:hidden');
        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', function() {
                const sidebar = document.querySelector('aside');
                sidebar.classList.toggle('hidden');
                sidebar.classList.toggle('fixed');
                sidebar.classList.toggle('inset-0');
                sidebar.classList.toggle('z-40');
                sidebar.classList.toggle('md:relative');
                sidebar.classList.toggle('md:flex');
            });
        }
    </script>
</body>
</html>
