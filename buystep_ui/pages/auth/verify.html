<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Account - Ubuntu Commerce Network</title>
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        secondary: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                            950: '#431407',
                        },
                        accent: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                            950: '#2e1065',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        display: ['Montserrat', 'sans-serif'],
                    },
                },
            },
        }
    </script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../css/styles.css">
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm">
        <nav class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="../../index.html" class="flex items-center">
                    <span class="text-primary-600 text-2xl font-display font-bold">Ubuntu</span>
                    <span class="text-gray-700 text-2xl font-display font-medium ml-1">Commerce</span>
                </a>
            </div>
            
            <div class="hidden md:flex items-center space-x-6">
                <a href="../../index.html" class="text-gray-700 hover:text-primary-600 font-medium">Home</a>
                <a href="../about.html" class="text-gray-700 hover:text-primary-600 font-medium">About</a>
                <a href="../how-it-works.html" class="text-gray-700 hover:text-primary-600 font-medium">How It Works</a>
                <a href="../contact.html" class="text-gray-700 hover:text-primary-600 font-medium">Contact</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <a href="login.html" class="text-primary-600 hover:text-primary-700 font-medium">Login</a>
                <a href="signup.html" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors">Sign Up</a>
            </div>
            
            <button class="md:hidden text-gray-700 focus:outline-none">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="max-w-md mx-auto">
            <div class="text-center">
                <h1 class="text-3xl font-display font-bold text-gray-900">Verify Your Account</h1>
                <p class="mt-2 text-sm text-gray-600">We've sent a verification code to your phone</p>
            </div>
            
            <div class="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Steps -->
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-sm"></i>
                            </div>
                            <span class="text-xs mt-1 text-green-600 font-medium">Account</span>
                        </div>
                        <div class="flex-1 h-1 bg-primary-600 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">2</span>
                            </div>
                            <span class="text-xs mt-1 text-primary-600 font-medium">Verification</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">3</span>
                            </div>
                            <span class="text-xs mt-1 text-gray-500 font-medium">Profile</span>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mb-6">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary-100 text-primary-600 mb-4">
                        <i class="fas fa-mobile-alt text-2xl"></i>
                    </div>
                    <p class="text-gray-700">Enter the 6-digit code sent to</p>
                    <p class="font-medium text-gray-900">+254 7XX XXX 123</p>
                </div>
                
                <form class="space-y-6" action="profile.html">
                    <div>
                        <label for="verification-code" class="sr-only">Verification Code</label>
                        <div class="flex justify-between space-x-2">
                            <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                        </div>
                    </div>

                    <div>
                        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Verify
                        </button>
                    </div>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">
                        Didn't receive the code? 
                        <button type="button" id="resend-button" class="font-medium text-primary-600 hover:text-primary-500">
                            Resend Code
                            <span id="countdown" class="text-gray-500">(60s)</span>
                        </button>
                    </p>
                </div>
                
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">
                        <a href="signup.html" class="flex items-center justify-center font-medium text-gray-600 hover:text-gray-900">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Sign Up
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h3 class="text-lg font-display font-bold mb-4">Ubuntu Commerce</h3>
                    <p class="text-gray-300 mb-4">Community-backed commerce platform for Africa.</p>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="../../index.html" class="text-gray-300 hover:text-white">Home</a></li>
                        <li><a href="../about.html" class="text-gray-300 hover:text-white">About Us</a></li>
                        <li><a href="../how-it-works.html" class="text-gray-300 hover:text-white">How It Works</a></li>
                        <li><a href="../contact.html" class="text-gray-300 hover:text-white">Contact Us</a></li>
                    </ul>
                </div>
                
                <!-- Legal -->
                <div>
                    <h3 class="text-sm font-bold mb-4">Legal</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#" class="text-gray-300 hover:text-white">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Cookie Policy</a></li>
                    </ul>
                </div>
                
                <!-- Contact -->
                <div>
                    <h3 class="text-sm font-bold mb-4">Contact Us</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-start">
                            <i class="fas fa-envelope mt-1 mr-3 text-gray-400"></i>
                            <span class="text-gray-300"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-300">
                <p>&copy; 2025 Ubuntu Commerce Network. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../../js/main.js"></script>
    <script>
        // OTP input handling
        const inputs = document.querySelectorAll('input[type="text"]');
        
        inputs.forEach((input, index) => {
            // Focus on first input on page load
            if (index === 0) {
                input.focus();
            }
            
            // Handle input
            input.addEventListener('input', function() {
                if (this.value.length === 1) {
                    // Move to next input
                    if (inputs[index + 1]) {
                        inputs[index + 1].focus();
                    }
                }
            });
            
            // Handle backspace
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Backspace' && !this.value) {
                    // Move to previous input
                    if (inputs[index - 1]) {
                        inputs[index - 1].focus();
                    }
                }
            });
            
            // Handle paste
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const pasteData = e.clipboardData.getData('text').slice(0, 6);
                
                // Fill inputs with pasted data
                for (let i = 0; i < pasteData.length; i++) {
                    if (inputs[i + index]) {
                        inputs[i + index].value = pasteData[i];
                        
                        // Focus on the next empty input or the last one
                        if (inputs[i + index + 1] && i < pasteData.length - 1) {
                            inputs[i + index + 1].focus();
                        } else {
                            inputs[Math.min(index + pasteData.length - 1, inputs.length - 1)].focus();
                        }
                    }
                }
            });
        });
        
        // Countdown timer for resend button
        let countdown = 60;
        const countdownElement = document.getElementById('countdown');
        const resendButton = document.getElementById('resend-button');
        
        resendButton.disabled = true;
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = `(${countdown}s)`;
            
            if (countdown <= 0) {
                clearInterval(timer);
                countdownElement.textContent = '';
                resendButton.disabled = false;
                resendButton.classList.add('text-primary-600', 'hover:text-primary-500');
                resendButton.classList.remove('text-gray-400');
            }
        }, 1000);
        
        resendButton.addEventListener('click', function() {
            if (!this.disabled) {
                // Simulate resending code
                this.disabled = true;
                this.classList.remove('text-primary-600', 'hover:text-primary-500');
                this.classList.add('text-gray-400');
                
                countdown = 60;
                countdownElement.textContent = `(${countdown}s)`;
                
                // Reset timer
                const newTimer = setInterval(() => {
                    countdown--;
                    countdownElement.textContent = `(${countdown}s)`;
                    
                    if (countdown <= 0) {
                        clearInterval(newTimer);
                        countdownElement.textContent = '';
                        resendButton.disabled = false;
                        resendButton.classList.add('text-primary-600', 'hover:text-primary-500');
                        resendButton.classList.remove('text-gray-400');
                    }
                }, 1000);
                
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success flex items-center justify-between mt-4';
                successMessage.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span>Verification code resent successfully!</span>
                    </div>
                    <button class="close text-green-800">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                const form = document.querySelector('form');
                form.parentNode.insertBefore(successMessage, form);
                
                // Remove message after 5 seconds
                setTimeout(() => {
                    successMessage.classList.add('opacity-0');
                    setTimeout(() => {
                        successMessage.remove();
                    }, 300);
                }, 5000);
                
                // Clear inputs
                inputs.forEach(input => {
                    input.value = '';
                });
                inputs[0].focus();
            }
        });
    </script>
</body>
</html>
