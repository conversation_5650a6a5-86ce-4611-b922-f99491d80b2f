<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Ubuntu Commerce Network</title>
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        secondary: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                            950: '#431407',
                        },
                        accent: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                            950: '#2e1065',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        display: ['Montserrat', 'sans-serif'],
                    },
                },
            },
        }
    </script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../css/styles.css">
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm">
        <nav class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="../../index.html" class="flex items-center">
                    <span class="text-primary-600 text-2xl font-display font-bold">Ubuntu</span>
                    <span class="text-gray-700 text-2xl font-display font-medium ml-1">Commerce</span>
                </a>
            </div>
            
            <div class="hidden md:flex items-center space-x-6">
                <a href="../../index.html" class="text-gray-700 hover:text-primary-600 font-medium">Home</a>
                <a href="../about.html" class="text-gray-700 hover:text-primary-600 font-medium">About</a>
                <a href="../how-it-works.html" class="text-gray-700 hover:text-primary-600 font-medium">How It Works</a>
                <a href="../contact.html" class="text-gray-700 hover:text-primary-600 font-medium">Contact</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <a href="login.html" class="text-primary-600 hover:text-primary-700 font-medium">Login</a>
                <a href="signup.html" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors">Sign Up</a>
            </div>
            
            <button class="md:hidden text-gray-700 focus:outline-none">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="max-w-2xl mx-auto">
            <div class="text-center">
                <h1 class="text-3xl font-display font-bold text-gray-900">Create Your Account</h1>
                <p class="mt-2 text-sm text-gray-600">Join Ubuntu Commerce and access community-backed financial services</p>
            </div>
            
            <div class="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Steps -->
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">1</span>
                            </div>
                            <span class="text-xs mt-1 text-primary-600 font-medium">Account</span>
                        </div>
                        <div class="flex-1 h-1 bg-primary-200 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">2</span>
                            </div>
                            <span class="text-xs mt-1 text-gray-500 font-medium">Verification</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">3</span>
                            </div>
                            <span class="text-xs mt-1 text-gray-500 font-medium">Profile</span>
                        </div>
                    </div>
                </div>
                
                <form class="space-y-6" action="verify.html">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first-name" class="form-label">First Name</label>
                            <input id="first-name" name="first-name" type="text" required class="form-input" placeholder="Enter your first name">
                        </div>
                        
                        <div>
                            <label for="last-name" class="form-label">Last Name</label>
                            <input id="last-name" name="last-name" type="text" required class="form-input" placeholder="Enter your last name">
                        </div>
                    </div>

                    <div>
                        <label for="phone" class="form-label">Phone Number</label>
                        <div class="flex">
                            <select class="form-input rounded-r-none w-24">
                                <option value="+254">+254</option>
                                <option value="+255">+255</option>
                                <option value="+256">+256</option>
                                <option value="+233">+233</option>
                                <option value="+234">+234</option>
                            </select>
                            <input id="phone" name="phone" type="tel" required class="form-input rounded-l-none flex-1" placeholder="Enter your phone number">
                        </div>
                        <p class="text-xs text-gray-500 mt-1">We'll send a verification code to this number</p>
                    </div>

                    <div>
                        <label for="email" class="form-label">Email Address (Optional)</label>
                        <input id="email" name="email" type="email" class="form-input" placeholder="Enter your email address">
                    </div>

                    <div>
                        <label for="password" class="form-label">Password</label>
                        <div class="relative">
                            <input id="password" name="password" type="password" required class="form-input pr-10" placeholder="Create a password">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <div class="flex items-center">
                                <div class="w-1/4 h-1 rounded-full bg-gray-200" id="strength-1"></div>
                                <div class="w-1/4 h-1 rounded-full bg-gray-200 ml-1" id="strength-2"></div>
                                <div class="w-1/4 h-1 rounded-full bg-gray-200 ml-1" id="strength-3"></div>
                                <div class="w-1/4 h-1 rounded-full bg-gray-200 ml-1" id="strength-4"></div>
                                <span class="ml-2 text-xs text-gray-500" id="strength-text">Weak</span>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-500 mt-2 space-y-1">
                            <li class="flex items-center">
                                <i class="fas fa-check text-gray-400 mr-1" id="check-length"></i>
                                At least 8 characters
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-gray-400 mr-1" id="check-number"></i>
                                At least 1 number
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-gray-400 mr-1" id="check-special"></i>
                                At least 1 special character
                            </li>
                        </ul>
                    </div>

                    <div>
                        <label for="confirm-password" class="form-label">Confirm Password</label>
                        <div class="relative">
                            <input id="confirm-password" name="confirm-password" type="password" required class="form-input pr-10" placeholder="Confirm your password">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="terms" name="terms" type="checkbox" required class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="terms" class="text-gray-700">
                                I agree to the 
                                <a href="#" class="text-primary-600 hover:text-primary-500">Terms of Service</a> and 
                                <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
                            </label>
                        </div>
                    </div>

                    <div>
                        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Continue
                        </button>
                    </div>
                </form>

                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or sign up with</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-2 gap-3">
                        <div>
                            <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <i class="fab fa-whatsapp text-green-600 text-lg"></i>
                                <span class="ml-2">WhatsApp</span>
                            </a>
                        </div>
                        <div>
                            <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-mobile-alt text-blue-600 text-lg"></i>
                                <span class="ml-2">USSD</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account? 
                        <a href="login.html" class="font-medium text-primary-600 hover:text-primary-500">Sign in</a>
                    </p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h3 class="text-lg font-display font-bold mb-4">Ubuntu Commerce</h3>
                    <p class="text-gray-300 mb-4">Community-backed commerce platform for Africa.</p>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="../../index.html" class="text-gray-300 hover:text-white">Home</a></li>
                        <li><a href="../about.html" class="text-gray-300 hover:text-white">About Us</a></li>
                        <li><a href="../how-it-works.html" class="text-gray-300 hover:text-white">How It Works</a></li>
                        <li><a href="../contact.html" class="text-gray-300 hover:text-white">Contact Us</a></li>
                    </ul>
                </div>
                
                <!-- Legal -->
                <div>
                    <h3 class="text-sm font-bold mb-4">Legal</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#" class="text-gray-300 hover:text-white">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Cookie Policy</a></li>
                    </ul>
                </div>
                
                <!-- Contact -->
                <div>
                    <h3 class="text-sm font-bold mb-4">Contact Us</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-start">
                            <i class="fas fa-envelope mt-1 mr-3 text-gray-400"></i>
                            <span class="text-gray-300"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-300">
                <p>&copy; 2025 Ubuntu Commerce Network. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../../js/main.js"></script>
    <script>
        // Password visibility toggle
        document.querySelectorAll('input[type="password"]').forEach(input => {
            const toggleButton = input.nextElementSibling;
            
            toggleButton.addEventListener('click', function() {
                const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                input.setAttribute('type', type);
                
                // Toggle icon
                const icon = toggleButton.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        });
        
        // Password strength checker
        const passwordInput = document.getElementById('password');
        const strengthBars = [
            document.getElementById('strength-1'),
            document.getElementById('strength-2'),
            document.getElementById('strength-3'),
            document.getElementById('strength-4')
        ];
        const strengthText = document.getElementById('strength-text');
        const checkLength = document.getElementById('check-length');
        const checkNumber = document.getElementById('check-number');
        const checkSpecial = document.getElementById('check-special');
        
        passwordInput.addEventListener('input', function() {
            const password = passwordInput.value;
            let strength = 0;
            
            // Check length
            if (password.length >= 8) {
                strength += 1;
                checkLength.classList.remove('text-gray-400');
                checkLength.classList.add('text-green-500');
            } else {
                checkLength.classList.remove('text-green-500');
                checkLength.classList.add('text-gray-400');
            }
            
            // Check for numbers
            if (/\d/.test(password)) {
                strength += 1;
                checkNumber.classList.remove('text-gray-400');
                checkNumber.classList.add('text-green-500');
            } else {
                checkNumber.classList.remove('text-green-500');
                checkNumber.classList.add('text-gray-400');
            }
            
            // Check for special characters
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                strength += 1;
                checkSpecial.classList.remove('text-gray-400');
                checkSpecial.classList.add('text-green-500');
            } else {
                checkSpecial.classList.remove('text-green-500');
                checkSpecial.classList.add('text-gray-400');
            }
            
            // Additional strength for length > 12
            if (password.length >= 12) {
                strength += 1;
            }
            
            // Update strength bars
            for (let i = 0; i < 4; i++) {
                if (i < strength) {
                    strengthBars[i].classList.remove('bg-gray-200');
                    
                    if (strength === 1) {
                        strengthBars[i].classList.add('bg-red-500');
                    } else if (strength === 2) {
                        strengthBars[i].classList.add('bg-yellow-500');
                    } else if (strength === 3) {
                        strengthBars[i].classList.add('bg-yellow-400');
                    } else {
                        strengthBars[i].classList.add('bg-green-500');
                    }
                } else {
                    strengthBars[i].className = 'w-1/4 h-1 rounded-full bg-gray-200 ml-1';
                    if (i === 0) {
                        strengthBars[i].classList.remove('ml-1');
                    }
                }
            }
            
            // Update strength text
            if (strength === 0) {
                strengthText.textContent = 'Weak';
                strengthText.className = 'ml-2 text-xs text-gray-500';
            } else if (strength === 1) {
                strengthText.textContent = 'Weak';
                strengthText.className = 'ml-2 text-xs text-red-500';
            } else if (strength === 2) {
                strengthText.textContent = 'Fair';
                strengthText.className = 'ml-2 text-xs text-yellow-500';
            } else if (strength === 3) {
                strengthText.textContent = 'Good';
                strengthText.className = 'ml-2 text-xs text-yellow-400';
            } else {
                strengthText.textContent = 'Strong';
                strengthText.className = 'ml-2 text-xs text-green-500';
            }
        });
        
        // Confirm password validation
        const confirmPasswordInput = document.getElementById('confirm-password');
        
        confirmPasswordInput.addEventListener('input', function() {
            if (passwordInput.value !== confirmPasswordInput.value) {
                confirmPasswordInput.classList.add('border-red-500');
                confirmPasswordInput.classList.add('focus:border-red-500');
                confirmPasswordInput.classList.add('focus:ring-red-500');
            } else {
                confirmPasswordInput.classList.remove('border-red-500');
                confirmPasswordInput.classList.remove('focus:border-red-500');
                confirmPasswordInput.classList.remove('focus:ring-red-500');
            }
        });
    </script>
</body>
</html>
