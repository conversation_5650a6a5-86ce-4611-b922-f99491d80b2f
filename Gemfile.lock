GEM
  remote: https://rubygems.org/
  specs:
    action_text-trix (2.1.15)
      railties
    actioncable (8.1.0.beta1)
      actionpack (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.1.0.beta1)
      actionpack (= 8.1.0.beta1)
      activejob (= 8.1.0.beta1)
      activerecord (= 8.1.0.beta1)
      activestorage (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      mail (>= 2.8.0)
    actionmailer (8.1.0.beta1)
      actionpack (= 8.1.0.beta1)
      actionview (= 8.1.0.beta1)
      activejob (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.1.0.beta1)
      actionview (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.1.0.beta1)
      action_text-trix (~> 2.1.15)
      actionpack (= 8.1.0.beta1)
      activerecord (= 8.1.0.beta1)
      activestorage (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      globalid (>= 0.3.6)
    activemodel (8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
    activerecord (8.1.0.beta1)
      activemodel (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      timeout (>= 0.4.0)
    activestorage (8.1.0.beta1)
      actionpack (= 8.1.0.beta1)
      activejob (= 8.1.0.beta1)
      activerecord (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      marcel (~> 1.0)
    activesupport (8.1.0.beta1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.1)
    bigdecimal (3.3.0)
    bindex (0.8.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.1.0)
      racc
    builder (3.3.0)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.4)
    crass (1.0.6)
    database_cleaner-active_record (2.2.2)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-passwordless (1.1.0)
      devise
      globalid
    diff-lcs (1.6.2)
    dotenv (3.1.8)
    drb (2.2.3)
    ed25519 (1.4.0)
    erb (5.0.3)
    erubi (1.13.1)
    et-orbi (1.4.0)
      tzinfo
    factory_bot (6.5.5)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.1)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    fugit (1.11.2)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.3.0)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.2.2)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.14.1)
      actionview (>= 7.0.0)
      activesupport (>= 7.0.0)
    json (2.13.2)
    kamal (2.7.0)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.4)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.1.0)
    matrix (0.4.3)
    mini_mime (1.1.5)
    minitest (5.26.0)
    monetize (1.13.0)
      money (~> 6.12)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.8.0)
    net-imap (0.5.12)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.10-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.10-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.10-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.10-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.10-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.10-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.10-x86_64-linux-musl)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    ostruct (0.6.3)
    parallel (1.27.0)
    parser (3.3.9.0)
      ast (~> 2.4.1)
      racc
    pg (1.6.2)
    pg (1.6.2-aarch64-linux)
    pg (1.6.2-aarch64-linux-musl)
    pg (1.6.2-arm64-darwin)
    pg (1.6.2-x86_64-linux)
    pg (1.6.2-x86_64-linux-musl)
    pp (0.6.3)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.3.1)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (7.0.4)
      nio4r (~> 2.0)
    pundit (2.5.2)
      activesupport (>= 3.0.0)
    pundit-matchers (3.1.2)
      rspec-core (~> 3.12)
      rspec-expectations (~> 3.12)
      rspec-mocks (~> 3.12)
      rspec-support (~> 3.12)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.2.2)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.1.0.beta1)
      actioncable (= 8.1.0.beta1)
      actionmailbox (= 8.1.0.beta1)
      actionmailer (= 8.1.0.beta1)
      actionpack (= 8.1.0.beta1)
      actiontext (= 8.1.0.beta1)
      actionview (= 8.1.0.beta1)
      activejob (= 8.1.0.beta1)
      activemodel (= 8.1.0.beta1)
      activerecord (= 8.1.0.beta1)
      activestorage (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      bundler (>= 1.15.0)
      railties (= 8.1.0.beta1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.1.0.beta1)
      actionpack (= 8.1.0.beta1)
      activesupport (= 8.1.0.beta1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      tsort (>= 0.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.15.0)
      erb
      psych (>= 4.0.0)
      tsort
    regexp_parser (2.11.3)
    reline (0.6.2)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.4)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.6)
    rubocop (1.81.1)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.47.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.47.1)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.26.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails (2.33.4)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-progressbar (1.13.0)
    rubyzip (3.1.1)
    securerandom (0.4.1)
    selenium-webdriver (4.36.0)
      base64 (~> 0.2)
      json (<= 2.13.2)
      logger (~> 1.4)
      prism (~> 1.0, < 1.5)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 4.0)
      websocket (~> 1.0)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    solid_cable (3.0.12)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_cache (1.0.7)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.2.1)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (>= 1.3.1)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    tailwindcss-rails (4.3.0)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 4.0)
    tailwindcss-ruby (4.1.13)
    tailwindcss-ruby (4.1.13-aarch64-linux-gnu)
    tailwindcss-ruby (4.1.13-aarch64-linux-musl)
    tailwindcss-ruby (4.1.13-arm64-darwin)
    tailwindcss-ruby (4.1.13-x86_64-linux-gnu)
    tailwindcss-ruby (4.1.13-x86_64-linux-musl)
    thor (1.4.0)
    thruster (0.1.15)
    thruster (0.1.15-aarch64-linux)
    thruster (0.1.15-arm64-darwin)
    thruster (0.1.15-x86_64-linux)
    timeout (0.4.3)
    tsort (0.2.0)
    turbo-rails (2.0.17)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.2.0)
      unicode-emoji (~> 4.1)
    unicode-emoji (4.1.0)
    uri (1.0.4)
    useragent (0.16.11)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin-25
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  bootsnap
  brakeman
  bundler-audit
  capybara
  database_cleaner-active_record (~> 2.2)
  debug
  devise (~> 4.9)
  devise-passwordless (~> 1.0)
  factory_bot_rails (~> 6.4)
  faker (~> 3.5)
  importmap-rails
  jbuilder
  kamal
  money-rails (~> 1.15)
  pg (~> 1.1)
  propshaft
  puma (>= 5.0)
  pundit (~> 2.3)
  pundit-matchers (~> 3.1)
  rails (~> 8.1.0.beta1)
  rspec-rails (~> 7.1)
  rubocop-rails-omakase
  selenium-webdriver
  shoulda-matchers (~> 6.4)
  solid_cable
  solid_cache
  solid_queue
  stimulus-rails
  tailwindcss-rails
  thruster
  turbo-rails
  tzinfo-data
  web-console

BUNDLED WITH
   2.7.2
