# Buystep Commerce Platform
## Comprehensive Agile Development Plan

**Project Duration:** 6 months (12 two-week sprints)  
**Team Size:** 4-6 developers, 1 QA, 1 Product Owner, 1 Designer  
**Methodology:** Scrum with 2-week sprints  
**Target:** MVP Launch after Sprint 10, Polish & Pilot in Sprints 11-12

---

# 📋 Table of Contents

1. [Sprint Overview](#sprint-overview)
2. [Detailed Sprint Plans](#detailed-sprint-plans)
3. [User Stories Library](#user-stories-library)
4. [Case Studies](#case-studies)
5. [Success Metrics](#success-metrics)

---

# Sprint Overview

| Sprint | Duration | Focus Area | Key Deliverables |
|--------|----------|------------|------------------|
| 0 | Week -2 to 0 | Infrastructure Setup | Rails app, CI/CD, staging environment |
| 1 | Week 1-2 | Foundation | User auth, basic models, admin panel |
| 2 | Week 3-4 | Foundation cont. | Profile management, KYC, notifications |
| 3 | Week 5-6 | Circles Core | Circle creation, membership, invites |
| 4 | Week 7-8 | Circles Advanced | Contributions, fund management, reputation |
| 5 | Week 9-10 | Transactions | Purchase flow, installments, repayments |
| 6 | Week 11-12 | Credit Engine | Scoring algorithm, limit calculation |
| 7 | Week 13-14 | Mobile Money | M-Pesa integration, payment processing |
| 8 | Week 15-16 | USSD Interface | USSD menu system, session management |
| 9 | Week 17-18 | WhatsApp Bot | Bot commands, payment confirmations |
| 10 | Week 19-20 | Merchants | Merchant onboarding, dashboard, payouts |
| 11 | Week 21-22 | Polish & Testing | Bug fixes, performance, security audit |
| 12 | Week 23-24 | Pilot Launch | Pilot deployment, monitoring, support |

---

# Detailed Sprint Plans

## Sprint 0: Infrastructure & Setup
**Duration:** 2 weeks (Pre-Sprint)  
**Goal:** Establish development infrastructure and team workflows

### Objectives
- [ ] Complete Rails 8 application setup
- [ ] Configure CI/CD pipeline
- [ ] Set up staging and production environments
- [ ] Establish team workflows and ceremonies
- [ ] Create initial database schema

### Technical Tasks
1. Run setup script (`buystep-commerce-setup.sh`)
2. Configure GitHub repository with branch protection
3. Set up GitHub Actions for CI/CD
4. Deploy to staging (Heroku/AWS)
5. Configure monitoring (Sentry, DataDog)
6. Set up database backups
7. Create initial seed data
8. Configure SSL certificates
9. Set up Redis and Sidekiq
10. Create project documentation structure

### User Stories
- None (infrastructure sprint)

### Definition of Done
- [ ] Rails app running on staging
- [ ] All tests passing in CI
- [ ] Team can deploy with one command
- [ ] Monitoring alerts configured
- [ ] Documentation complete

### Risks
- Infrastructure delays could impact Sprint 1
- Learning curve for Rails 8 features

---

## Sprint 1: Foundation - Authentication & User Management
**Duration:** Weeks 1-2  
**Goal:** Core user management and authentication system

### Objectives
- [ ] User registration and login
- [ ] Phone number verification
- [ ] Basic user profiles
- [ ] Role-based access control
- [ ] Admin panel basics

### User Stories

#### US-001: User Registration via Phone Number
**As a** new user  
**I want to** register using my phone number  
**So that** I can access the Buystep Commerce platform

**Acceptance Criteria:**
- Given I'm on the registration page
- When I enter my phone number (+************)
- And I enter my desired password
- Then I should receive an SMS verification code
- And when I enter the correct code
- Then my account should be created
- And I should be logged in

**Technical Notes:**
- Use Devise for authentication
- Integrate Africa's Talking for SMS
- Phone number must be unique
- Support country codes (KE, NG, GH, UG, TZ)

**Story Points:** 5

---

#### US-002: User Login
**As a** registered user  
**I want to** log in with my phone number and password  
**So that** I can access my account

**Acceptance Criteria:**
- Given I have a registered account
- When I enter my phone number and password
- Then I should be logged into my dashboard
- And I should see a personalized welcome message

**Story Points:** 2

---

#### US-003: Profile Completion
**As a** new user  
**I want to** complete my profile with basic information  
**So that** I can access platform features

**Acceptance Criteria:**
- Given I'm a newly registered user
- When I navigate to profile setup
- Then I should be prompted to enter:
  - First name and last name
  - Date of birth
  - Country
  - Preferred currency
- And when I submit the form
- Then my profile should be saved
- And I should be redirected to the dashboard

**Story Points:** 3

---

#### US-004: Admin User Management
**As an** admin  
**I want to** view and manage all users  
**So that** I can moderate the platform

**Acceptance Criteria:**
- Given I'm logged in as an admin
- When I navigate to the admin panel
- Then I should see a list of all users
- And I should be able to:
  - Search users by phone/email
  - View user details
  - Suspend/unsuspend users
  - View user activity logs

**Story Points:** 5

---

### Technical Tasks
1. Configure Devise with phone number authentication
2. Create User model with STI (Consumer, Merchant, Admin)
3. Build SMS verification service
4. Create user profile forms
5. Build admin dashboard with user management
6. Implement Pundit policies for authorization
7. Add user activity tracking
8. Create comprehensive RSpec tests
9. Design responsive UI with TailwindCSS

### Dependencies
- Africa's Talking API account
- Twilio account (backup)

### Definition of Done
- [ ] Users can register and verify phone numbers
- [ ] Login/logout works correctly
- [ ] Profile management functional
- [ ] Admin can manage users
- [ ] 90%+ test coverage
- [ ] All acceptance criteria met
- [ ] Security audit passed

---

## Sprint 2: Foundation - KYC & Notifications
**Duration:** Weeks 3-4  
**Goal:** Identity verification and notification system

### Objectives
- [ ] KYC document upload
- [ ] Identity verification workflow
- [ ] Email and SMS notifications
- [ ] In-app notification center
- [ ] User settings management

### User Stories

#### US-005: KYC Document Upload
**As a** user  
**I want to** upload my ID document  
**So that** I can verify my identity and access credit

**Acceptance Criteria:**
- Given I'm logged in
- When I navigate to KYC section
- Then I should be able to:
  - Upload national ID photo (front/back)
  - Take a selfie for face verification
  - Enter ID number
- And when I submit
- Then my documents should be queued for review
- And I should receive a confirmation notification

**Story Points:** 5

---

#### US-006: Notification System
**As a** user  
**I want to** receive notifications about important events  
**So that** I stay informed about my account activity

**Acceptance Criteria:**
- Given I have an account
- When important events occur (contribution due, purchase approved, etc.)
- Then I should receive notifications via:
  - SMS (for critical events)
  - Email (for updates)
  - In-app notifications
- And I should be able to view notification history
- And I should be able to manage notification preferences

**Story Points:** 8

---

#### US-007: User Settings
**As a** user  
**I want to** manage my account settings  
**So that** I can control my preferences

**Acceptance Criteria:**
- Given I'm logged in
- When I navigate to settings
- Then I should be able to:
  - Change password
  - Update phone number (with verification)
  - Set notification preferences
  - Change preferred language
  - Update currency preference
- And changes should be saved immediately

**Story Points:** 3

---

### Technical Tasks
1. Create KYC document model and storage (ActiveStorage)
2. Build verification workflow with status tracking
3. Integrate Smile Identity or Onfido API
4. Create notification service with Noticed gem
5. Build SMS gateway abstraction
6. Create email templates
7. Build in-app notification center
8. Create user settings controller and views
9. Add notification tests

### Dependencies
- Smile Identity/Onfido API
- Cloud storage (AWS S3/DO Spaces)

---

## Sprint 3: Circles Core - Creation & Membership
**Duration:** Weeks 5-6  
**Goal:** Core circle functionality - creation, joining, and management

### Objectives
- [ ] Circle creation workflow
- [ ] Member invitations
- [ ] Membership approval flow
- [ ] Circle discovery
- [ ] Basic circle dashboard

### User Stories

#### US-008: Create a Circle
**As a** user  
**I want to** create a guarantee circle  
**So that** I can pool funds with trusted friends/family

**Acceptance Criteria:**
- Given I'm logged in
- When I navigate to "Create Circle"
- Then I should be able to enter:
  - Circle name (3-100 characters)
  - Description
  - Monthly contribution amount
  - Maximum members (5-50)
  - Public/private visibility
- And when I submit
- Then the circle should be created with status "forming"
- And I should be automatically added as creator/admin
- And I should receive a confirmation notification

**Story Points:** 8

---

#### US-009: Invite Members to Circle
**As a** circle admin  
**I want to** invite people to join my circle  
**So that** we can reach the minimum member threshold

**Acceptance Criteria:**
- Given I'm a circle admin
- When I navigate to my circle's "Members" page
- Then I should see an "Invite Members" button
- And when I click it, I should be able to:
  - Enter phone numbers (one or multiple)
  - Generate an invite link
  - Share via SMS/WhatsApp
- And invitees should receive an SMS with the invitation
- And they should be able to join via the link

**Story Points:** 5

---

#### US-010: Join a Circle
**As a** user  
**I want to** join a circle I've been invited to  
**So that** I can participate in group savings

**Acceptance Criteria:**
- Given I've received a circle invitation
- When I click the invite link
- Then I should see circle details:
  - Circle name
  - Current members count
  - Monthly contribution amount
  - Circle reputation score
- And I should be able to click "Request to Join"
- And my request should be sent to circle admins
- And I should receive a "Request Pending" notification

**Story Points:** 5

---

#### US-011: Approve/Reject Membership Requests
**As a** circle admin  
**I want to** review and approve membership requests  
**So that** I can maintain circle quality

**Acceptance Criteria:**
- Given I'm a circle admin
- When someone requests to join
- Then I should receive a notification
- And when I navigate to "Pending Requests"
- Then I should see all pending requests with:
  - Applicant name
  - Reputation score
  - Other circles they belong to
- And I should be able to approve or reject
- And the applicant should be notified of the decision

**Story Points:** 5

---

#### US-012: Browse Public Circles
**As a** user  
**I want to** discover and browse public circles  
**So that** I can find circles to join

**Acceptance Criteria:**
- Given I'm logged in
- When I navigate to "Discover Circles"
- Then I should see a list of public circles
- And I should be able to filter by:
  - Contribution amount range
  - Reputation score
  - Available spots
  - Location (optional)
- And I should be able to view circle details
- And I should be able to request to join

**Story Points:** 5

---

#### US-013: View Circle Dashboard
**As a** circle member  
**I want to** view my circle's dashboard  
**So that** I can track circle activity and health

**Acceptance Criteria:**
- Given I'm a circle member
- When I navigate to my circle's page
- Then I should see:
  - Circle name and description
  - Member list with contribution status
  - Total fund balance
  - Total credit capacity
  - My personal credit limit
  - Recent activity feed
  - Contribution status (paid/pending)
- And the data should update in real-time

**Story Points:** 8

---

### Technical Tasks
1. Create Circle, Membership, GuaranteeFund models (complete from architecture)
2. Build CircleCreationService
3. Build MembershipService (invite, join, approve, reject)
4. Create circle invitation system with unique tokens
5. Build circle discovery with search/filter
6. Create circle dashboard with Turbo Frames
7. Implement real-time updates with ActionCable
8. Create Pundit policies for circles
9. Add comprehensive RSpec tests
10. Design circle UI components

### Dependencies
- Sprint 1 & 2 completed
- SMS gateway functional

### Definition of Done
- [ ] Users can create circles
- [ ] Invitation flow works end-to-end
- [ ] Membership approval functional
- [ ] Circle discovery works
- [ ] Dashboard displays correctly
- [ ] All tests passing
- [ ] UI is mobile-responsive

---

## Sprint 4: Circles Advanced - Contributions & Fund Management
**Duration:** Weeks 7-8  
**Goal:** Circle financial operations and reputation system

### Objectives
- [ ] Contribution processing
- [ ] Mobile money integration (M-Pesa)
- [ ] Fund balance tracking
- [ ] Reputation scoring
- [ ] Credit limit calculation
- [ ] Circle activation

### User Stories

#### US-014: Make a Contribution
**As a** circle member  
**I want to** contribute my monthly amount to the circle fund  
**So that** I maintain my good standing and credit access

**Acceptance Criteria:**
- Given I'm an active circle member
- And my contribution is due
- When I navigate to my circle dashboard
- Then I should see a "Contribute Now" button
- And when I click it
- Then I should be prompted for payment method:
  - M-Pesa
  - Bank transfer
  - Airtel Money
- And when I select M-Pesa
- Then I should receive an STK push prompt
- And when I enter my PIN
- Then the payment should be processed
- And the circle fund should be updated
- And I should receive a confirmation SMS
- And my contribution status should show "Paid"

**Story Points:** 13

---

#### US-015: View Contribution History
**As a** circle member  
**I want to** view my contribution history  
**So that** I can track my payments

**Acceptance Criteria:**
- Given I'm a circle member
- When I navigate to "My Contributions"
- Then I should see a list of all contributions:
  - Date
  - Amount
  - Status (paid/pending/failed)
  - Payment method
  - Receipt/reference number
- And I should be able to download receipts

**Story Points:** 3

---

#### US-016: Circle Activation
**As a** circle admin  
**I want my** circle to automatically activate  
**When** we reach minimum requirements  
**So that** members can start accessing credit

**Acceptance Criteria:**
- Given I created a circle
- And we have minimum 5 members
- And each member has made their first contribution
- Then the circle status should automatically change to "active"
- And all members should receive activation notification
- And members should now be able to access credit

**Story Points:** 5

---

#### US-017: View Circle Fund Balance
**As a** circle member  
**I want to** see the current fund balance  
**So that** I understand our collective credit capacity

**Acceptance Criteria:**
- Given I'm a circle member
- When I view the circle dashboard
- Then I should see:
  - Total fund balance (KES 50,000)
  - Total contributions this month (KES 10,000)
  - Total credit capacity (Balance × 10)
  - Available credit capacity
  - Allocated credit (currently borrowed)
- And the balance should update in real-time

**Story Points:** 3

---

#### US-018: Track Member Contribution Status
**As a** circle admin  
**I want to** see which members have paid their contributions  
**So that** I can follow up with delinquent members

**Acceptance Criteria:**
- Given I'm a circle admin
- When I navigate to "Member Contributions"
- Then I should see all members with:
  - Name
  - Current month status (✓ Paid / ⚠ Pending / ✗ Missed)
  - Total contributions to date
  - Missed contribution count
  - Last contribution date
- And I should be able to send reminders to pending members

**Story Points:** 5

---

#### US-019: Reputation Score Display
**As a** circle member  
**I want to** see my reputation score and circle reputation  
**So that** I understand my standing

**Acceptance Criteria:**
- Given I'm a circle member
- When I view my profile or circle dashboard
- Then I should see:
  - My personal reputation score (0-100)
  - Factors affecting my score
  - Circle reputation score
  - How reputation affects my credit limit
- And scores should be color-coded:
  - Green (80-100): Excellent
  - Yellow (60-79): Good
  - Orange (40-59): Fair
  - Red (0-39): Poor

**Story Points:** 5

---

#### US-020: Credit Limit Calculation
**As a** circle member  
**I want to** see my personal credit limit  
**So that** I know how much I can borrow

**Acceptance Criteria:**
- Given I'm an active circle member
- When I view my dashboard
- Then I should see:
  - My personal credit limit
  - How it's calculated:
    - Base: Total contributions × 10
    - Reputation multiplier
    - Circle capacity constraint
  - Available credit (limit - current borrowed)
  - Used credit

**Story Points:** 5

---

### Technical Tasks
1. Create Contribution model
2. Build M-Pesa integration service
3. Implement STK Push flow
4. Create payment callback handler
5. Build ContributionProcessingService
6. Implement fund balance tracking
7. Create ReputationCalculator service
8. Build CreditLimitCalculator service
9. Create circle activation logic
10. Build contribution reminder job (Sidekiq)
11. Add comprehensive payment tests
12. Create payment reconciliation dashboard

### Dependencies
- M-Pesa Daraja API credentials
- Sprint 3 completed

### Definition of Done
- [ ] Contributions work end-to-end
- [ ] M-Pesa integration functional
- [ ] Fund balances track correctly
- [ ] Reputation scores calculate accurately
- [ ] Credit limits update automatically
- [ ] Circle activation works
- [ ] Payment tests passing
- [ ] Security audit for payment flow

---

## Sprint 5: Transactions - Purchase Flow
**Duration:** Weeks 9-10  
**Goal:** Consumer purchase and BNPL payment flow

### Objectives
- [ ] Purchase request system
- [ ] Credit check and approval
- [ ] Installment plan generation
- [ ] Purchase confirmation
- [ ] Transaction history

### User Stories

#### US-021: Make a Purchase (Consumer)
**As a** consumer  
**I want to** buy a product and pay later  
**So that** I can purchase items I need without paying upfront

**Acceptance Criteria:**
- Given I'm at a merchant's shop
- And the merchant has entered the purchase amount (KES 3,000)
- When the merchant enters my phone number
- Then I should receive a purchase request notification
- And I should see:
  - Merchant name
  - Purchase amount
  - Available payment plans:
    - Pay in 30 days (0% fee)
    - Pay in 4 (0% fee)
    - Pay in 6 (5% fee)
    - Pay in 12 (10% fee)
- And when I select a payment plan
- Then I should see the installment breakdown
- And when I confirm with my PIN
- Then the purchase should be approved
- And the merchant should receive instant confirmation

**Story Points:** 13

---

#### US-022: Credit Check Before Purchase
**As a** consumer  
**I want the** system to check my credit eligibility  
**Before** approving my purchase  
**So that** I don't exceed my credit limit

**Acceptance Criteria:**
- Given I have a personal credit limit of KES 5,000
- And I've already used KES 2,000
- When I try to make a purchase of KES 4,000
- Then the system should:
  - Check my available credit (KES 3,000)
  - Deny the purchase (insufficient credit)
  - Show me how much I can borrow (KES 3,000)
  - Suggest paying down existing debt first

**Story Points:** 8

---

#### US-023: View Purchase History
**As a** consumer  
**I want to** view all my past and current purchases  
**So that** I can track my spending

**Acceptance Criteria:**
- Given I'm logged in
- When I navigate to "My Purchases"
- Then I should see all purchases:
  - Active purchases (with outstanding balance)
  - Completed purchases
- And for each purchase I should see:
  - Merchant name
  - Purchase date
  - Total amount
  - Payment plan
  - Outstanding balance
  - Next payment due date
  - Status (active/completed/defaulted)

**Story Points:** 5

---

#### US-024: View Installment Schedule
**As a** consumer  
**I want to** view my payment schedule  
**So that** I know when payments are due

**Acceptance Criteria:**
- Given I have an active purchase
- When I click on the purchase
- Then I should see the installment schedule:
  - Installment #1: KES 750 - Due Mar 15 - Status: Paid
  - Installment #2: KES 750 - Due Mar 29 - Status: Paid
  - Installment #3: KES 750 - Due Apr 12 - Status: Pending
  - Installment #4: KES 750 - Due Apr 26 - Status: Pending
- And I should see total paid vs remaining
- And I should be able to make early payment

**Story Points:** 3

---

#### US-025: Receive Purchase Confirmation
**As a** consumer  
**I want to** receive confirmation of my purchase  
**So that** I have a record of the transaction

**Acceptance Criteria:**
- Given I completed a purchase
- Then I should immediately receive:
  - SMS confirmation with transaction ID
  - Email receipt with full details
  - In-app notification
- And the confirmation should include:
  - Merchant name
  - Purchase amount
  - Payment plan selected
  - First payment due date
  - Transaction reference

**Story Points:** 3

---

#### US-026: Process Purchase (Merchant)
**As a** merchant  
**I want to** process a customer's BNPL purchase  
**So that** I can complete the sale

**Acceptance Criteria:**
- Given I'm logged in as a merchant
- When I navigate to "New Sale"
- Then I should be able to:
  - Enter purchase amount
  - Enter customer phone number
  - Add item description (optional)
- And when I submit
- Then the customer should receive the purchase request
- And I should see "Waiting for customer approval"
- And when the customer approves
- Then I should see "Purchase Approved"
- And I should receive confirmation
- And I should be informed when I'll receive payout (24 hours)

**Story Points:** 8

---

### Technical Tasks
1. Create Purchase, Installment models
2. Build PurchaseRequestService
3. Create CreditCheckService
4. Build InstallmentScheduleGenerator
5. Create PurchaseApprovalService
6. Build real-time notification system
7. Create merchant purchase interface
8. Build transaction history views
9. Add fraud detection checks
10. Create comprehensive transaction tests

### Dependencies
- Sprint 4 completed
- Credit system functional

### Definition of Done
- [ ] Purchase flow works end-to-end
- [ ] Credit checks functional
- [ ] Installment schedules generate correctly
- [ ] Notifications sent properly
- [ ] Transaction history displays
- [ ] Merchant interface works
- [ ] Tests passing
- [ ] Fraud checks implemented

---

## Sprint 6: Transactions - Repayments & Collections
**Duration:** Weeks 11-12  
**Goal:** Payment collection and default management

### User Stories

#### US-027: Make a Payment
**As a** consumer  
**I want to** pay my installments  
**So that** I fulfill my obligations and maintain good credit

**Acceptance Criteria:**
- Given I have an outstanding installment
- When I navigate to "My Purchases"
- And I select the purchase
- Then I should see "Pay Now" button
- And when I click it
- Then I should be able to:
  - Select payment method (M-Pesa, Bank, etc.)
  - Choose to pay one installment or full balance
- And I should receive STK Push
- And when payment succeeds
- Then the installment should be marked paid
- And my credit should be released
- And I should receive confirmation

**Story Points:** 8

---

#### US-028: Receive Payment Reminders
**As a** consumer  
**I want to** receive reminders before payments are due  
**So that** I don't miss payments

**Acceptance Criteria:**
- Given I have an installment due in 3 days
- Then I should receive an SMS reminder:
  - "Reminder: KES 750 payment due on Apr 12"
- And I should receive another reminder:
  - 1 day before due date
  - On the due date (if unpaid)
- And I should receive in-app notifications

**Story Points:** 3

---

#### US-029: Late Payment Handling
**As the** system  
**I want to** apply late fees for missed payments  
**So that** users are incentivized to pay on time

**Acceptance Criteria:**
- Given a payment is due today
- And the user hasn't paid by end of day
- Then starting tomorrow:
  - Day 1-7: Apply 1% late fee
  - Day 8-14: Apply 2% late fee
  - Day 15-30: Apply 5% late fee
- And send late payment notification
- And after 30 days, mark as default
- And report to circle

**Story Points:** 8

---

#### US-030: Default Management
**As the** system  
**I want to** handle defaults properly  
**So that** circle guarantees are activated

**Acceptance Criteria:**
- Given a payment is 30+ days overdue
- Then the system should:
  - Mark purchase as defaulted
  - Notify the user's circle(s)
  - Trigger circle guarantee process
  - Update member reputation score
  - Update circle reputation score
  - Suspend member borrowing privileges
- And circle admins should be notified
- And collections process should begin

**Story Points:** 13

---

#### US-031: Early Payment Option
**As a** consumer  
**I want to** pay off my purchase early  
**So that** I can free up my credit limit

**Acceptance Criteria:**
- Given I have outstanding installments
- When I navigate to purchase details
- Then I should see "Pay Full Balance" option
- And when I click it
- Then I should see:
  - Current outstanding balance
  - No early payment penalty
  - Confirmation prompt
- And when I confirm and pay
- Then all remaining installments should be marked paid
- And my credit should be fully released

**Story Points:** 5

---

### Technical Tasks
1. Create Repayment model
2. Build RepaymentService
3. Create payment reminder job
4. Build late fee calculator
5. Create default management service
6. Build collections workflow
7. Integrate circle guarantee activation
8. Create payment reconciliation
9. Add repayment tests

---

## Sprint 7: Credit Engine
**Duration:** Weeks 13-14  
**Goal:** Sophisticated credit scoring and risk management

### User Stories

#### US-032: Credit Score Calculation
**As the** system  
**I want to** calculate user credit scores accurately  
**So that** we can make informed lending decisions

**Acceptance Criteria:**
- Given a user has:
  - 3 circles (avg reputation: 75)
  - 10 purchases (8 on-time, 2 late)
  - Mobile money data (consistent transactions)
  - 5 trusted connections
- Then the credit score should be calculated:
  - Circle reputation (40%): 75 × 0.4 = 30
  - Payment history (30%): 80% × 30 = 24
  - Mobile money (20%): 70 × 0.2 = 14
  - Social network (10%): 80 × 0.1 = 8
  - Total: 76/100 (Low Risk)

**Story Points:** 13

---

#### US-033: View Credit Profile
**As a** user  
**I want to** view my detailed credit profile  
**So that** I understand my creditworthiness

**Acceptance Criteria:**
- Given I'm logged in
- When I navigate to "My Credit"
- Then I should see:
  - Credit score (0-100)
  - Risk category
  - Score breakdown by component
  - Total credit limit
  - Available credit
  - Credit utilization
  - Payment history (on-time %)
  - Factors improving/hurting my score
  - Tips to improve score

**Story Points:** 5

---

### Technical Tasks
1. Create CreditProfile model
2. Build CreditScoringService
3. Implement scoring algorithm
4. Create credit limit calculator
5. Build credit profile dashboard
6. Add credit reassessment job
7. Create credit tests

---

## Sprint 8: Mobile Money Integration
**Duration:** Weeks 15-16  
**Goal:** M-Pesa and mobile money payment processing

### User Stories

#### US-034: M-Pesa Payment
**As a** user  
**I want to** pay using M-Pesa  
**So that** I can use my preferred payment method

**Acceptance Criteria:**
- Given I'm making a payment
- When I select M-Pesa
- Then I should receive STK Push on my phone
- And when I enter my M-Pesa PIN
- Then payment should be processed
- And I should receive M-Pesa confirmation
- And payment should be recorded in the system
- And I should receive platform confirmation

**Story Points:** 13

---

### Technical Tasks
1. Integrate M-Pesa Daraja API
2. Build STK Push service
3. Create callback handler
4. Build payment status checker
5. Add payment reconciliation
6. Create payment tests
7. Add error handling

---

## Sprint 9: USSD Interface
**Duration:** Weeks 17-18  
**Goal:** USSD menu system for feature phones

### User Stories

#### US-035: Access Platform via USSD
**As a** user without a smartphone  
**I want to** access Buystep Commerce via USSD  
**So that** I can use the platform on my feature phone

**Acceptance Criteria:**
- Given I dial *384#
- Then I should see main menu:
  - 1. Check Balance
  - 2. My Circles
  - 3. Make Purchase
  - 4. Contribute to Circle
  - 5. Register
- And I should be able to navigate menus
- And I should be able to complete transactions

**Story Points:** 21

---

### Technical Tasks
1. Integrate Africa's Talking USSD
2. Build USSD session manager
3. Create menu router
4. Build all USSD flows
5. Add USSD tests

---

## Sprint 10: Merchant Features
**Duration:** Weeks 19-20  
**Goal:** Merchant onboarding, dashboard, and payouts

### User Stories

#### US-036: Merchant Onboarding
**As a** business owner  
**I want to** register as a merchant  
**So that** I can accept BNPL payments

**Acceptance Criteria:**
- Given I'm registering
- When I select "Merchant Account"
- Then I should provide:
  - Business name
  - Business type
  - Location
  - Bank account for payouts
  - Business documents (optional)
- And when I submit
- Then my account should be under review
- And I should be notified within 24 hours

**Story Points:** 8

---

#### US-037: Merchant Dashboard
**As a** merchant  
**I want to** view my sales and earnings  
**So that** I can track my business performance

**Acceptance Criteria:**
- Given I'm a verified merchant
- When I log in
- Then I should see:
  - Today's sales
  - Weekly/monthly sales
  - Pending payouts
  - Customer analytics
  - Top-selling products

**Story Points:** 8

---

### Technical Tasks
1. Create Merchant STI model
2. Build merchant onboarding
3. Create merchant dashboard
4. Build payout system
5. Add merchant tests

---

## Sprint 11: Polish & Testing
**Duration:** Weeks 21-22  
**Goal:** Bug fixes, performance optimization, security

### Objectives
- [ ] Fix all known bugs
- [ ] Performance optimization
- [ ] Security audit
- [ ] Load testing
- [ ] Documentation

---

## Sprint 12: Pilot Launch
**Duration:** Weeks 23-24  
**Goal:** Deploy to production, launch pilot, gather feedback

### Objectives
- [ ] Production deployment
- [ ] Onboard pilot users (1,000 target)
- [ ] Create 100 circles
- [ ] Process first transactions
- [ ] Gather feedback
- [ ] Monitor system health

---

# User Stories Library

## Epic: User Management
- US-001 to US-007

## Epic: Circles
- US-008 to US-020

## Epic: Transactions
- US-021 to US-031

## Epic: Credit
- US-032 to US-033

## Epic: Payments
- US-034

## Epic: USSD
- US-035

## Epic: Merchants
- US-036 to US-037

---

# Case Studies

## Case Study 1: Joyce - Market Vendor Success Story

### Background
Joyce is a 28-year-old vegetable vendor in Gikomba Market, Nairobi. She earns KES 15,000-20,000 per month but struggles with cash flow. She has no bank account or credit history.

### Journey

**Week 1: Discovery**
- Joyce's friend Mary tells her about Buystep Commerce
- Mary is already in a circle and making purchases
- Joyce visits Mary's stall and sees Mary bought a new phone via BNPL

**Week 2: Registration**
- Joyce dials *384# on her feature phone
- Registers with phone number: +************
- Receives verification SMS
- Completes basic profile

**Week 3: Joining a Circle**
- Mary invites Joyce to "Gikomba Market Vendors Circle"
- Joyce receives invitation SMS
- She reviews circle details:
  - 8 current members
  - KES 500 monthly contribution
  - 10x credit multiplier
  - Reputation: 72 (Good)
- Joyce requests to join
- Circle admin (Mary) approves her

**Week 4: First Contribution**
- Joyce receives SMS: "Time to contribute KES 500"
- She dials *384# → My Circles → Contribute
- Selects M-Pesa payment
- Receives STK Push, enters PIN
- Contribution confirmed
- Her credit limit: KES 5,000 (500 × 10)

**Month 2: First Purchase**
- Joyce needs a new solar lamp for her stall (KES 2,500)
- She visits electronics shop (merchant on platform)
- Merchant enters amount + Joyce's phone number
- Joyce receives purchase request via SMS
- She selects "Pay in 4" (4 payments of KES 625)
- Confirms with PIN
- Merchant gets instant confirmation
- Joyce takes the lamp home

**Months 3-5: Building Credit**
- Joyce makes all 4 payments on time
- Her reputation score increases: 50 → 65
- She contributes monthly to circle (never missed)
- Her credit limit increases to KES 8,000
- She makes 3 more purchases (phone charger, shoes, cooking pot)
- All paid on time

**Month 6: Expansion**
- Joyce wants to buy bulk vegetables (KES 15,000)
- Her limit is now KES 10,000 (not enough)
- She joins a second circle (wholesale vendors)
- Contributes KES 1,000/month
- Combined credit limit: KES 20,000
- She buys the bulk vegetables
- Sells them at profit
- Pays back early

**Outcome After 6 Months:**
- Credit score: 78 (Low Risk)
- Total purchases: 8 (KES 45,000)
- All payments on time
- Member of 2 circles
- Credit limit: KES 20,000
- Starting to save for business expansion

---

## Case Study 2: David - Merchant Success

### Background
David owns a small electronics shop in Lagos. He loses sales daily because customers can't pay upfront. Traditional payment processors charge 3-5% fees (too high for his margins).

### Journey

**Week 1: Discovery**
- David sees flyer for Buystep Commerce at tech meetup
- "Accept Pay Later payments, 1.5% fee, instant settlement"
- He's skeptical but signs up online

**Week 2: Onboarding**
- David registers as merchant
- Uploads business documents
- Provides bank account for payouts
- Verification takes 36 hours
- Account approved

**Week 3: First Sale**
- Young customer wants phone (NGN 45,000)
- Customer can't pay full amount
- David suggests "Pay over 4 installments"
- Customer agrees, provides phone number
- David enters sale in merchant portal
- Customer approves via SMS
- Transaction complete in 2 minutes
- David gets confirmation: "You'll receive NGN 44,325 tomorrow"
- (NGN 45,000 - 1.5% fee = NGN 44,325)

**Month 2: Growing Sales**
- David processes 15 BNPL sales (NGN 520,000)
- Average order value up 35% (people buy more when they can pay later)
- Receives payouts within 24 hours
- Only 1 customer defaults (circle covers it)

**Month 3: Inventory Financing**
- David wants to buy bulk phones for better pricing
- Needs NGN 800,000 (only has NGN 300,000)
- Applies for inventory financing
- Platform approves NGN 500,000 (based on sales velocity)
- 8% fee, 60 days to repay
- David buys bulk, gets 15% supplier discount
- Profit: NGN 120,000 - NGN 40,000 (fee) = NGN 80,000

**Month 6: Results**
- Monthly sales increased 40%
- Average order value up 35%
- New customers from BNPL access
- Inventory financing used 3 times
- Net increase in profit: NGN 180,000/month

---

## Case Study 3: Sarah & Family - Diaspora Guarantee

### Background
Sarah lives in London, sends £200/month to family in Kenya. She worries money isn't used wisely. Her brother John needs items but lacks credit.

### Journey

**Month 1: Setup**
- Sarah reads about diaspora guarantee program
- Creates account online
- Deposits £500 guarantee fund (earns 2.5% APY)
- Links brother John as beneficiary
- Sets credit limit: KES 50,000 (£500 × 100 exchange × 10x)

**Month 2: First Purchase**
- John needs smartphone for online business (KES 25,000)
- He requests purchase via platform
- Sarah receives WhatsApp notification:
  - "John wants to buy: Samsung Galaxy A14"
  - "Amount: KES 25,000"
  - "Merchant: Phone Palace, Nairobi"
  - "Payment plan: Pay in 6 months"
- Sarah approves
- John gets phone, pays KES 4,167/month

**Months 3-8: Trust Building**
- John makes all payments on time
- Sarah approves 3 more purchases:
  - Laptop for business (KES 40,000)
  - Business inventory (KES 15,000)
  - Motorcycle for deliveries (KES 80,000)
- John's credit limit increases to KES 100,000
- Sarah's guarantee fund earns interest
- No defaults, no deductions needed

**Month 9: Impact**
- John's online business thriving
- He's making KES 45,000/month
- All purchases paid on time
- Sarah confident in how money is used
- She increases guarantee to £1,000
- John's limit: KES 100,000
- Family relationship stronger (transparency)

---

# Success Metrics

## Sprint Velocity Tracking
- Target: 40-50 story points per sprint
- Track actual vs planned
- Adjust planning based on velocity

## Quality Metrics
- Test coverage: 90%+
- Code review: 100% of PRs
- Bug escape rate: <5%
- Security issues: 0 critical

## User Metrics (Post-Launch)
- User registration: 1,000 in first month
- Circle creation: 100 circles
- Transaction volume: KES 5M GMV
- Default rate: <5%
- User satisfaction: 80%+ NPS

---

**Document Version:** 1.0  
**Last Updated:** January 2025  
**Next Review:** After Sprint 6